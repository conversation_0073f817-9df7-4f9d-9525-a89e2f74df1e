# Trace MVP

Cardano blockchain traceability application for creating and sharing trace records on preview and mainnet networks.

## Quick Start

### Development

You can use npm, pnpm or bun

```bash
npm install
npm run db:push        # Setup database schema
npm run dev:seed       # Seed with sample data
npm run dev            # Start development server
```

### Production
```bash
npm install
npm run db:push        # Setup database schema only, no migrations atm
npm run build
npm run start
```

## Environment Variables

Create `.env` file with required variables, check .env.example

## Database Commands

```bash
npm run db:generate    # Generate migrations
npm run db:migrate     # Run migrations
npm run db:push        # Push schema directly
npm run db:studio      # Open Drizzle Studio
```

## Tech Stack

- **Framework**: Next.js 15 with Pages Router
- **Database**: PostgreSQL with Drizzle ORM
- **Styling**: Tailwind CSS + shadcn/ui
- **State**: Zustand

## Authentication

This app uses blockchain-based authentication leveraging Cardano wallet signatures. No passwords or traditional auth required.

### How it Works

1. **Wallet Connection** - User connects their Cardano wallet (<PERSON><PERSON>p<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>roi)
2. **Nonce Request** - Server generates a unique nonce with expiration (5 minutes)
3. **Message Signing** - Wallet signs a message containing:
   - Domain: `https://trace.zengate.global`
   - Action: `Sign to verify wallet ownership`
   - Wallet address
   - Server nonce
4. **Signature Verification** - Server verifies the signature belongs to the claimed wallet
5. **Session Creation** - 24-hour session established upon successful verification

### Security Features

- **No Passwords** - Authentication tied to wallet private keys
- **Replay Protection** - For this version, nonces are unique - in the future, server should generate nonces
- **Wallet Ownership** - Only the wallet owner can produce valid signatures
- **Session Management** - Sessions expire after 24 hours, stored client-side only
- **Network Verification** - Ensures wallet matches selected network (preview/mainnet)

## Docker Deployment

### Local Docker Build
```bash
docker build -t trace-mvp .
docker run -p 3000:3000 --env-file .env trace-mvp
```

### Google Cloud Run Deployment

1. **Enable standalone output** - Already configured in `next.config.ts`
2. **Build and push to Container Registry**:
```bash
gcloud builds submit --tag gcr.io/[PROJECT-ID]/trace-mvp
```
3. **Deploy to Cloud Run**:
```bash
gcloud run deploy trace-mvp \
  --image gcr.io/[PROJECT-ID]/trace-mvp \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 3000
```

### CI/CD with Cloud Build

The `cloudbuild.yaml` file is configured for automatic deployments on push to main branch.

## Considerations

- Still bugs to clean up 
- Atm, this is a version to publish for internal testing