#!/usr/bin/env tsx

/**
 * Database Seed Script
 * 
 * Seeds the database with mock data including:
 * - Wallets with verified Cardano addresses
 * - Traceability records with Winter protocol integration
 * - Payment hashes, IPFS hashes, and transaction data
 * - Mixed mainnet/preview network data for testing
 */

import * as dotenv from 'dotenv';
import path from 'path';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { wallets, records, type NewWallet, type NewRecord } from '../src/lib/db/schema';

// Mock Cardano wallet addresses (realistic format)
const mockWallets: NewWallet[] = [
  {
    walletAddress: 'addr1qx5y7zj9k8l3m4n6p2q8r5s9t4u7v2w5x8y3z6a9b4c7d2e5f8g1h4j7k2l9m6n3p8q5r2s9t6u3v8w5x2y9z6a3b8c5d2e9f6',
    walletName: 'lace',
    nonce: 'nonce123abc',
    walletSignature: '{"signature":"abc123def456","key":"mock_key_1"}',
    isVerified: true,
    role: 'user'
  },
  {
    walletAddress: 'addr1qy8z6a5b2c9d6e3f0g9h2i5j8k3l6m9n2o5p8q3r6s9t2u5v8w3x6y9z2a5b8c3d6e9f2g5h8i3j6k9l2m5n8o3p6q9r2s5t8u3v6',
    walletName: 'vespr',
    nonce: 'nonce456def',
    walletSignature: '{"signature":"def456ghi789","key":"mock_key_2"}',
    isVerified: true,
    role: 'user'
  },
  {
    walletAddress: 'addr1qz3a8b5c2d9e6f3g0h9i2j5k8l3m6n9o2p5q8r3s6t9u2v5w8x3y6z9a2b5c8d3e6f9g2h5i8j3k6l9m2n5o8p3q6r9s2t5u8v3w6',
    walletName: 'vespr',
    nonce: 'nonce789ghi',
    walletSignature: '{"signature":"ghi789jkl012","key":"mock_key_3"}',
    isVerified: true,
    role: 'user'
  }
];

// Generate realistic Cardano transaction hashes
const generateTxHash = (): string => {
  const chars = '0123456789abcdef';
  return Array.from({ length: 64 }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
};

// Generate realistic IPFS hashes
const generateIpfsHash = (): string => {
  const chars = '**********************************************************';
  return 'Qm' + Array.from({ length: 44 }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
};

// Generate realistic payment transaction hashes (same format as tx hash)
const generatePaymentHash = (): string => {
  const chars = '0123456789abcdef';
  return Array.from({ length: 64 }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
};

// Generate realistic Winter Protocol IDs (UUID format)
const generateWinterId = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// Generate Winter status options
const getRandomWinterStatus = (): string => {
  const statuses = ['PENDING', 'SUCCESS', 'FAILED', 'PROCESSING'];
  return statuses[Math.floor(Math.random() * statuses.length)];
};

// Mock JSON data for different types of records
const mockRecordsData = [
  {
    name: 'Coffee Bean Batch #4521',
    origin: 'Guatemala',
    farm: 'La Esperanza Coffee Farm',
    harvestDate: '2024-03-15',
    processingMethod: 'Washed',
    altitude: '1400-1600m',
    variety: 'Bourbon',
    certifications: ['Organic', 'Fair Trade'],
    qualityScore: 87.5
  },
  {
    name: 'Organic Tomato Harvest',
    farm: 'Sunny Valley Organics',
    location: 'California, USA',
    harvestDate: '2024-07-20',
    variety: 'Cherokee Purple',
    certifications: ['USDA Organic', 'Non-GMO'],
    pesticidesUsed: 'None',
    irrigationMethod: 'Drip irrigation',
    yieldPerAcre: '25 tons'
  },
  {
    name: 'Wine Bottle #W2024-0847',
    vineyard: 'Château Margaux',
    vintage: '2021',
    grape: 'Cabernet Sauvignon',
    region: 'Bordeaux, France',
    alcoholContent: '13.5%',
    bottlingDate: '2024-02-10',
    storage: 'Temperature controlled cellar',
    productionVolume: '5000 bottles'
  },
  {
    name: 'Smartphone Model XY-2024',
    manufacturer: 'TechCorp Industries',
    model: 'TechPhone Pro',
    serialNumber: 'TP240157890',
    manufacturingDate: '2024-06-12',
    components: {
      processor: 'OctoCore A15',
      memory: '128GB',
      battery: '4000mAh Li-ion'
    },
    qualityTests: ['Water resistance', 'Drop test', 'Battery life'],
    warranty: '2 years'
  },
  {
    name: 'Pharmaceutical Batch #PH-2024-3456',
    drugName: 'VitaminD Plus',
    manufacturer: 'HealthCorp Pharmaceuticals',
    batchNumber: 'VD240345',
    manufacturingDate: '2024-05-08',
    expiryDate: '2026-05-08',
    activeIngredients: ['Vitamin D3 1000IU', 'Calcium 200mg'],
    testResults: {
      purity: '99.8%',
      potency: '101.2%',
      dissolution: 'Pass'
    },
    regulatoryApproval: 'FDA Approved'
  },
  {
    name: 'Diamond Certificate #DIA2024789',
    carat: 1.25,
    cut: 'Round Brilliant',
    color: 'G',
    clarity: 'VS1',
    certification: 'GIA Certified',
    origin: 'Botswana',
    miningDate: '2024-01-20',
    polishingDate: '2024-04-15',
    measurements: '6.8 x 6.9 x 4.2 mm'
  },
  {
    name: 'Luxury Handbag #LX-2024-0156',
    brand: 'Elite Leather Co.',
    model: 'Milano Collection',
    material: 'Italian Leather',
    color: 'Midnight Black',
    craftedDate: '2024-08-10',
    artisan: 'Marco Benedetti',
    serialNumber: 'ELC240156',
    authentication: 'Holographic seal + NFC chip'
  },
  {
    name: 'Beef Cut Traceability',
    farm: 'Green Pastures Ranch',
    animalId: 'GP-COW-4521',
    breed: 'Angus',
    birthDate: '2022-05-12',
    feedType: 'Grass-fed organic',
    slaughterDate: '2024-06-30',
    processingFacility: 'Premium Meat Processing',
    cutType: 'Ribeye Steak',
    packagingDate: '2024-07-01'
  },
  {
    name: 'Solar Panel Module #SP-2024-8901',
    manufacturer: 'SolarTech Solutions',
    model: 'EcoPanel 400W',
    serialNumber: 'ST240890',
    manufacturingDate: '2024-04-20',
    efficiency: '21.2%',
    warranty: '25 years',
    certifications: ['IEC 61215', 'IEC 61730'],
    siliconSource: 'Grade A polysilicon',
    qualityGrade: 'Tier 1'
  },
  {
    name: 'Artisan Cheese Wheel #AC-2024-3344',
    cheesemaker: 'Alpine Dairy Cooperative',
    cheeseType: 'Aged Gruyère',
    milkSource: 'Grass-fed Holstein cows',
    productionDate: '2024-01-15',
    agingPeriod: '18 months',
    caveLocation: 'Cave #7 - Temperature 12°C',
    saltType: 'Sea salt from Guérande',
    weight: '35kg',
    qualityGrade: 'AOC Certified'
  }
];

async function seed() {
  console.log('🌱 Starting database seed...');

  // Check if DATABASE_URL is set
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is not set');
    console.log('   Please make sure you have a .env file with DATABASE_URL configured');
    process.exit(1);
  }

  console.log('🔗 Database URL:', process.env.DATABASE_URL.replace(/:[^:@]*@/, ':****@'));

  // Create database connection for the seed script
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    max: 5, // Reduced for seed script
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 10000,
  });

  const db = drizzle(pool);

  try {
    // Test database connection first
    console.log('🔍 Testing database connection...');
    await db.select().from(wallets).limit(1);
    console.log('✅ Database connection successful');

    // Clear existing data
    console.log('🧹 Clearing existing records...');
    await db.delete(records);
    await db.delete(wallets);

    // Insert wallets
    console.log('👛 Seeding wallets...');
    const insertedWallets = await db.insert(wallets).values(mockWallets).returning();
    console.log(`✅ Inserted ${insertedWallets.length} wallets`);

    // Create records with references to wallets
    console.log('📝 Seeding records...');
    const mockRecords: NewRecord[] = mockRecordsData.map((data, index) => {
      const winterStatus = getRandomWinterStatus();
      const isMainnet = Math.random() > 0.2; // 80% mainnet, 20% preview
      
      return {
        walletId: insertedWallets[index % insertedWallets.length].id, // Distribute records among wallets
        txHash: generateTxHash(),
        paymentHash: generatePaymentHash(), // Required payment hash
        ipfs: generateIpfsHash(),
        json: data,
        isPublic: Math.random() > 0.3, // 70% public, 30% private
        networkId: isMainnet ? 'mainnet' : 'preview',
        winterId: generateWinterId(), // Winter protocol token ID
        winterPublished: winterStatus === 'SUCCESS', // Published if successful
        winterStatus: winterStatus // Winter protocol status
      };
    });

    const insertedRecords = await db.insert(records).values(mockRecords).returning();
    console.log(`✅ Inserted ${insertedRecords.length} records`);

    // Display summary
    console.log('\n📊 Seeding Summary:');
    console.log(`   Wallets: ${insertedWallets.length}`);
    console.log(`   Records: ${insertedRecords.length}`);
    console.log(`   Public Records: ${insertedRecords.filter(r => r.isPublic).length}`);
    console.log(`   Private Records: ${insertedRecords.filter(r => !r.isPublic).length}`);
    console.log(`   Mainnet Records: ${insertedRecords.filter(r => r.networkId === 'mainnet').length}`);
    console.log(`   Preview Records: ${insertedRecords.filter(r => r.networkId === 'preview').length}`);
    console.log(`   Winter Published: ${insertedRecords.filter(r => r.winterPublished).length}`);
    console.log(`   Winter Pending: ${insertedRecords.filter(r => r.winterStatus === 'PENDING').length}`);
    
    console.log('\n🔗 Sample Record IDs (for testing):');
    insertedRecords.slice(0, 3).forEach(record => {
      console.log(`   ${record.id} - ${(record.json as any).name} [${record.networkId}] [${record.winterStatus}]`);
    });

    console.log('\n🎉 Database seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await pool.end();
    console.log('🔌 Database connection closed');
  }
}

// Run the seed function
seed().catch(console.error);