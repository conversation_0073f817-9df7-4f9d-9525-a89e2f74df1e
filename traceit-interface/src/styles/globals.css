@import url("https://fonts.googleapis.com/css2?family=Azeret+Mono:ital,wght@0,100..900;1,100..900&family=JetBrains+Mono:ital,wght@0,100..800;1,100..800&family=Joan&display=swap");
@import "tailwindcss";
@import "tw-animate-css";


@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.2178 0 0);
  --card: oklch(0.9702 0 0);
  --card-foreground: oklch(0.2178 0 0);
  --popover: oklch(0.9702 0 0);
  --popover-foreground: oklch(0.2178 0 0);
  --primary: oklch(0.6410 0.1668 131.7697);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9219 0 0);
  --secondary-foreground: oklch(0.2178 0 0);
  --muted: oklch(0.9551 0 0);
  --muted-foreground: oklch(0.4748 0 0);
  --accent: oklch(0.8699 0 0);
  --accent-foreground: oklch(0.2178 0 0);
  --destructive: oklch(0.6122 0.2082 22.2410);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9067 0 0);
  --input: oklch(0.9067 0 0);
  --ring: oklch(0.6410 0.1668 131.7697);
  --chart-1: oklch(0.7862 0.2061 149.4831);
  --chart-2: oklch(0.8278 0.1131 57.9984);
  --chart-3: oklch(0.9321 0.0791 201.7882);
  --chart-4: oklch(0.7679 0.1893 324.9188);
  --chart-5: oklch(0.7606 0.1124 73.7444);
  --sidebar: oklch(0.9656 0.0060 350.7980);
  --link: oklch(96.798% 0.21094 109.783);
  --sidebar-foreground: oklch(0.2569 0.0169 352.4042);
  --sidebar-primary: oklch(0.6410 0.1668 131.7697);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9149 0.1839 135.8680);
  --sidebar-accent-foreground: oklch(0.3095 0.0677 145.9897);
  --sidebar-border: oklch(0.9119 0.0111 348.4514);
  --sidebar-ring: oklch(0.6410 0.1668 131.7697);
  --font-sans: Joan, serif;
  --font-serif: JetBrains Mono, monospace;
  --font-mono: Azeret Mono, monospace;
  --radius: 0.625rem;
  --shadow-2xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-sm: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow-md: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 2px 4px -4px hsl(0 0% 0% / 0.09);
  --shadow-lg: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 4px 6px -4px hsl(0 0% 0% / 0.09);
  --shadow-xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 8px 10px -4px hsl(0 0% 0% / 0.09);
  --shadow-2xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.22);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2891 0 0);
  --foreground: oklch(0.8839 0.0088 44.8812);
  --card: oklch(0.3132 0 0);
  --card-foreground: oklch(0.9158 0 0);
  --popover: oklch(0.3132 0 0);
  --popover-foreground: oklch(0.9397 0.0119 51.3156);
  --primary: oklch(0.9043 0.2442 132.5144);
  --primary-foreground: oklch(0.3095 0.0677 145.9897);
  --secondary: oklch(0.3904 0 0);
  --secondary-foreground: oklch(0.9397 0.0119 51.3156);
  --muted: oklch(0.3523 0 0);
  --muted-foreground: oklch(0.7155 0 0);
  --accent: oklch(0.9612 0 0);
  --accent-foreground: oklch(0.3203 0.0193 157.2157);
  --destructive: oklch(0.6122 0.2082 22.2410);
  --destructive-foreground: oklch(1.0000 0 0);
  --link: oklch(96.798% 0.21094 109.783);
  --border: oklch(0.3904 0 0);
  --input: oklch(0.5103 0 0);
  --ring: oklch(0.6410 0.1668 131.7697);
  --chart-1: oklch(0.7862 0.2061 149.4831);
  --chart-2: oklch(0.8278 0.1131 57.9984);
  --chart-3: oklch(0.9321 0.0791 201.7882);
  --chart-4: oklch(0.7679 0.1893 324.9188);
  --chart-5: oklch(0.7606 0.1124 73.7444);
  --sidebar: oklch(0.2569 0.0169 352.4042);
  --sidebar-foreground: oklch(0.9397 0.0119 51.3156);
  --sidebar-primary: oklch(0.9095 0.2118 134.9762);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9149 0.1839 135.8680);
  --sidebar-accent-foreground: oklch(0.2569 0.0169 352.4042);
  --sidebar-border: oklch(0.3637 0.0203 342.2664);
  --sidebar-ring: oklch(0.9019 0.2196 137.6484);
  --link: oklch(96.798% 0.21094 109.783);
  --font-sans: Joan, serif;
  --font-serif: JetBrains Mono, monospace;
  --font-mono: Azeret Mono, monospace;
  --radius: 0.625rem;
  --shadow-2xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-xs: 0px 6px 12px -3px hsl(0 0% 0% / 0.04);
  --shadow-sm: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 1px 2px -4px hsl(0 0% 0% / 0.09);
  --shadow-md: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 2px 4px -4px hsl(0 0% 0% / 0.09);
  --shadow-lg: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 4px 6px -4px hsl(0 0% 0% / 0.09);
  --shadow-xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.09), 0px 8px 10px -4px hsl(0 0% 0% / 0.09);
  --shadow-2xl: 0px 6px 12px -3px hsl(0 0% 0% / 0.22);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-link: var(--link);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}


@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-serif
  }
   
  }


@theme inline {
  --animate-marquee: marquee var(--duration) infinite linear;
  --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
 
  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }
  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }
}