import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useWallet } from '@meshsdk/react'
import { useWalletVerification } from '@/lib/store/wallet-verification'
import { QRCodeShare } from '@/lib/components/QRCodeShare'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/lib/components/ui/card'
import { Badge } from '@/lib/components/ui/badge'
import { Button } from '@/lib/components/ui/button'
import { Separator } from '@/lib/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/lib/components/ui/tabs'
import { 
  ExternalLink, 
  Loader2, 
  Lock, 
  AlertCircle, 
  User, 
  Calendar,
  Hash,
  Database,
  ArrowLeft,
  Code,
  FileText
} from 'lucide-react'
import { toast } from 'sonner'
import { motion, AnimatePresence } from 'motion/react'
import { codeToHtml } from 'shiki'

interface RecordDetail {
  id: string
  txHash: string
  ipfs: string | null
  json: any
  isPublic: boolean
  networkId: string
  createdAt: string
  owner: {
    walletAddress: string
    walletName: string | null
  }
}

export default function RecordDetailPage() {
  const router = useRouter()
  const { id } = router.query
  const { wallet, connected } = useWallet()
  const { isVerified, walletAddress: verifiedWalletAddress } = useWalletVerification()
  
  const [record, setRecord] = useState<RecordDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isPrivate, setIsPrivate] = useState(false)
  const [isOwner, setIsOwner] = useState(false)
  const [highlightedJson, setHighlightedJson] = useState<string>('')
  const [activeTab, setActiveTab] = useState<string>('basic')

  const generateHighlightedJson = async (jsonData: any) => {
    try {
      const html = await codeToHtml(JSON.stringify(jsonData, null, 2), {
        lang: 'json',
        theme: 'ayu-dark',
        colorReplacements: {
          '#0b0e14': '#2e2e2e',
        }
      })
      setHighlightedJson(html)
    } catch (error) {
      console.error('Failed to highlight JSON:', error)
      setHighlightedJson('')
    }
  }

  useEffect(() => {
    if (!id || typeof id !== 'string') return

    const fetchRecord = async () => {
      setLoading(true)
      setError(null)
      setIsPrivate(false)
      setIsOwner(false)

      try {
        // Build URL with optional verified wallet address
        const url = isVerified && verifiedWalletAddress 
          ? `/api/records/${id}?walletAddress=${encodeURIComponent(verifiedWalletAddress)}`
          : `/api/records/${id}`
          
        console.log('🔍 Fetching record:', { 
          id, 
          isVerified, 
          hasVerifiedAddress: !!verifiedWalletAddress,
          url 
        })
          
        const response = await fetch(url)
        const data = await response.json()

        if (data.success) {
          setRecord(data.record)
          setIsOwner(data.isOwner || false)
          // Generate highlighted JSON when record is loaded
          if (data.record.json) {
            generateHighlightedJson(data.record.json)
          }
          console.log('✅ Record fetched successfully:', { isOwner: data.isOwner })
        } else {
          if (data.isPrivate) {
            setIsPrivate(true)
            console.log('🔒 Record is private')
          }
          setError(data.message || 'Failed to fetch record')
        }
      } catch (err) {
        console.error('Error fetching record:', err)
        setError('Failed to fetch record')
      } finally {
        setLoading(false)
      }
    }

    fetchRecord()
  }, [id, isVerified, verifiedWalletAddress])

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    })
  }

  // Format wallet address for display
  const formatWalletAddress = (address: string) => {
    return `${address.slice(0, 15)}...${address.slice(-15)}`
  }

  // Generate external URLs
  const getCardanoScanUrl = (txHash: string, networkId: string) => {
    const baseUrl = networkId === 'mainnet' 
      ? 'https://cardanoscan.io' 
      : 'https://preprod.cardanoscan.io'
    return `${baseUrl}/transaction/${txHash}`
  }

  const getIpfsUrl = (ipfsHash: string) => {
    return `https://ipfs.io/ipfs/${ipfsHash}`
  }

  // Render JSON data beautifully with animations
  const renderJsonData = (jsonData: any) => {
    if (!jsonData || typeof jsonData !== 'object') {
      return (
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-muted-foreground"
        >
          No data available
        </motion.div>
      )
    }

    return (
      <div className="space-y-4">
        {Object.entries(jsonData).map(([key, value], index) => (
          <motion.div 
            key={key} 
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.05, duration: 0.3 }}
            className="border-l-2 border-muted pl-4"
          >
            <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </div>
            <div className="mt-1">
              {typeof value === 'object' && value !== null ? (
                <pre className="text-sm bg-muted p-2 rounded text-wrap">
                  {JSON.stringify(value, null, 2)}
                </pre>
              ) : (
                <div className="text-sm">
                  {String(value)}
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen py-6 md:py-12 px-4 md:px-6">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="py-20">
              <div className="flex items-center justify-center">
                <Loader2 className="w-8 h-8 animate-spin mr-3" />
                <span>Loading record details...</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (error && !isPrivate) {
    return (
      <div className="min-h-screen py-6 md:py-12 px-4 md:px-6">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Button 
              variant="ghost" 
              onClick={() => router.back()}
              className="mb-6"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            
            <Card>
              <CardContent className="py-20 text-center">
                <AlertCircle className="w-16 h-16 text-muted-foreground mx-auto mb-6" />
                <h2 className="text-2xl font-bold mb-4">Record Not Found</h2>
                <p className="text-muted-foreground mb-6">
                  This record does not exist or may have been removed.
                </p>
                <Button onClick={() => router.push('/records')}>
                  View Your Records
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  if (isPrivate) {
    return (
      <div className="min-h-screen py-6 md:py-12 px-4 md:px-6">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Button 
              variant="ghost" 
              onClick={() => router.back()}
              className="mb-6"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            
            <Card>
              <CardContent className="py-20 text-center">
                <Lock className="w-16 h-16 text-muted-foreground mx-auto mb-6" />
                <h2 className="text-2xl font-bold mb-4">Private Record</h2>
                <p className="text-muted-foreground mb-6">
                  This record is private and cannot be viewed publicly.
                </p>
                <Button onClick={() => router.push('/records')}>
                  View Your Records
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  if (!record) {
    return null
  }

  return (
    <div className="min-h-screen py-4 sm:py-6 md:py-12 px-4 sm:px-6">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Record header */}
          <div className="mb-6 sm:mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-3">
              <h1 className="text-2xl sm:text-3xl font-bold">Record Details</h1>
              <div className="flex items-center gap-2">
                <Badge variant={record.networkId === 'mainnet' ? 'default' : 'secondary'}>
                  {record.networkId}
                </Badge>
                <Badge variant={record.isPublic ? "outline" : "secondary"}>
                  {record.isPublic ? 'Public' : 'Private'}
                </Badge>
              </div>
            </div>
            <p className="text-muted-foreground text-sm sm:text-base">
              ID: <span className="font-mono break-all">{record.id}</span>
            </p>
            
            {/* Owner message for private records */}
            {!record.isPublic && isOwner && (
              <div className="mt-4 p-3 bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                <div className="flex items-start space-x-2">
                  <Lock className="w-4 h-4 text-amber-600 dark:text-amber-500 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-amber-800 dark:text-amber-200">
                    <strong>You&apos;re viewing your own private record.</strong> This record is not public and only you can see it.
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="grid gap-4 sm:gap-6">
            {/* Owner Information */}
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.4, delay: 0.1 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="w-5 h-5 mr-2" />
                    Record Owner
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <motion.div 
                    className="space-y-3"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3, duration: 0.3 }}
                  >
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4, duration: 0.3 }}
                    >
                      <div className="text-sm text-muted-foreground">Wallet Address</div>
                      <div className="font-mono text-sm">
                        {formatWalletAddress(record.owner.walletAddress)}
                      </div>
                    </motion.div>
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5, duration: 0.3 }}
                    >
                      <div className="text-sm text-muted-foreground">Created</div>
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-2" />
                        {formatDate(record.createdAt)}
                      </div>
                    </motion.div>
                  </motion.div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Blockchain Information */}
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.4, delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Hash className="w-5 h-5 mr-2" />
                    Blockchain Details
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <motion.div 
                    className="space-y-4"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.4, duration: 0.3 }}
                  >
                    <motion.div
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5, duration: 0.3 }}
                    >
                      <div className="text-sm text-muted-foreground mb-2">Transaction Hash</div>
                      <div className="flex items-center space-x-2">
                        <code className="bg-muted px-2 py-1 rounded text-sm font-mono">
                          {record.txHash}
                        </code>
                        <Button size="sm" variant="outline" asChild>
                          <a
                            href={getCardanoScanUrl(record.txHash, record.networkId)}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <ExternalLink className="w-3 h-3 mr-1" />
                            View on CardanoScan
                          </a>
                        </Button>
                      </div>
                    </motion.div>
                    
                    {record.ipfs && (
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.6, duration: 0.3 }}
                      >
                        <div className="text-sm text-muted-foreground mb-2">IPFS Hash</div>
                        <div className="flex items-center space-x-2">
                          <code className="bg-muted px-2 py-1 rounded text-sm font-mono">
                            {record.ipfs}
                          </code>
                          <Button size="sm" variant="outline" asChild>
                            <a
                              href={getIpfsUrl(record.ipfs)}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <ExternalLink className="w-3 h-3 mr-1" />
                              View on IPFS
                            </a>
                          </Button>
                        </div>
                      </motion.div>
                    )}
                  </motion.div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Record Data */}
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.4, delay: 0.3 }}
            >
              <Card>
                <CardHeader>
                  <motion.div 
                    className="flex items-center justify-between"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5, duration: 0.3 }}
                  >
                    <div>
                      <CardTitle className="flex items-center">
                        <Database className="w-5 h-5 mr-2" />
                        Record Data
                      </CardTitle>
                      <CardDescription>
                        The data stored in this traceability record
                      </CardDescription>
                    </div>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.7, duration: 0.3 }}
                    >
                      <div className="grid grid-cols-2 w-fit bg-muted p-1 rounded-md">
                        <button
                          onClick={() => setActiveTab('basic')}
                          className={`flex items-center gap-1 text-xs px-3 py-1 rounded-sm transition-all duration-200 ${
                            activeTab === 'basic'
                              ? 'bg-background text-foreground shadow-sm'
                              : 'text-muted-foreground hover:text-foreground'
                          }`}
                        >
                          <FileText className="w-3 h-3" />
                          Basic
                        </button>
                        <button
                          onClick={() => setActiveTab('advanced')}
                          className={`flex items-center gap-1 text-xs px-3 py-1 rounded-sm transition-all duration-200 ${
                            activeTab === 'advanced'
                              ? 'bg-background text-foreground shadow-sm'
                              : 'text-muted-foreground hover:text-foreground'
                          }`}
                        >
                          <Code className="w-3 h-3" />
                          Advanced
                        </button>
                      </div>
                    </motion.div>
                  </motion.div>
                </CardHeader>
                
                <CardContent>
                  <AnimatePresence mode="wait">
                    {activeTab === 'basic' && (
                      <motion.div
                        key="basic"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        transition={{ duration: 0.25, ease: "easeInOut" }}
                      >
                        {renderJsonData(record.json)}
                      </motion.div>
                    )}
                    
                    {activeTab === 'advanced' && (
                      <motion.div
                        key="advanced"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        transition={{ duration: 0.25, ease: "easeInOut" }}
                      >
                        <div className="rounded-lg overflow-auto max-h-96 border bg-muted">
                          {highlightedJson ? (
                            <motion.div 
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.1, duration: 0.3 }}
                              className="text-xs md:text-sm font-mono [&_pre]:p-4 [&_pre]:m-0 [&_pre]:bg-transparent [&_pre]:font-mono"
                              dangerouslySetInnerHTML={{ __html: highlightedJson }}
                            />
                          ) : (
                            <motion.pre 
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.1, duration: 0.3 }}
                              className="bg-muted p-3 md:p-4 text-xs md:text-sm font-mono text-foreground"
                            >
                              {JSON.stringify(record.json, null, 2)}
                            </motion.pre>
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </CardContent>
              </Card>
            </motion.div>

            {/* QR Code Share - Only for public records */}
            {record.isPublic && (
              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.4, delay: 0.4 }}
              >
                <QRCodeShare 
                  recordId={record.id}
                  recordTitle={record.json?.name || 'Trace Record'}
                  isPublic={record.isPublic}
                />
              </motion.div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  )
}