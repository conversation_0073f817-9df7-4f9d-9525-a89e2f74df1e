import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { GetServerSideProps } from 'next'
import QRCode from 'qrcode'
import { Button } from '@/lib/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/lib/components/ui/card'
import { Badge } from '@/lib/components/ui/badge'
import { 
  QrCode, 
  Share2, 
  Copy, 
  Download, 
  ArrowLeft,
  ExternalLink,
  Loader2,
  Check,
  Eye
} from 'lucide-react'
import { toast } from 'sonner'
import { motion } from 'motion/react'

interface QRPageProps {
  recordId: string
  baseUrl: string
  recordExists: boolean
  isPublic: boolean
  recordTitle?: string
}

export default function QRPage({ recordId, baseUrl, recordExists, isPublic, recordTitle }: QRPageProps) {
  const router = useRouter()
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [copied, setCopied] = useState(false)
  
  const recordUrl = `${baseUrl}/records/${recordId}`
  const qrUrl = `${baseUrl}/records/${recordId}/qr`

  useEffect(() => {
    if (recordExists && isPublic) {
      generateQRCode()
    } else {
      setLoading(false)
    }
  }, [recordId, recordExists, isPublic])

  const generateQRCode = async () => {
    try {
      setLoading(true)
      const qrDataUrl = await QRCode.toDataURL(recordUrl, {
        width: 600, // Larger for dedicated page and mobile screenshots
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M'
      })
      setQrCodeDataUrl(qrDataUrl)
    } catch (error) {
      console.error('Error generating QR code:', error)
      toast.error('Failed to generate QR code')
    } finally {
      setLoading(false)
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${recordTitle || 'Trace Record'} - QR Code`,
          text: `QR code for blockchain traceability record`,
          url: qrUrl,
        })
      } catch (error) {
        if (error instanceof Error && error.name !== 'AbortError') {
          console.error('Error sharing:', error)
          fallbackCopyToClipboard()
        }
      }
    } else {
      fallbackCopyToClipboard()
    }
  }

  const fallbackCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(recordUrl)
      setCopied(true)
      toast.success('Record link copied to clipboard!')
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Error copying to clipboard:', error)
      toast.error('Failed to copy link')
    }
  }

  const handleDownloadQR = () => {
    if (!qrCodeDataUrl) return
    
    const link = document.createElement('a')
    link.download = `trace-record-${recordId}-qr.png`
    link.href = qrCodeDataUrl
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    toast.success('QR code downloaded!')
  }

  if (!recordExists) {
    return (
      <div className="min-h-screen py-6 md:py-12 px-4 md:px-6">
        <div className="max-w-2xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            
            <Card>
              <CardContent className="py-20 text-center">
                <QrCode className="w-16 h-16 text-muted-foreground mx-auto mb-6" />
                <h2 className="text-2xl font-bold mb-4">Record Not Found</h2>
                <p className="text-muted-foreground">
                  The requested record does not exist or may have been removed.
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  if (!isPublic) {
    return (
      <div className="min-h-screen py-6 md:py-12 px-4 md:px-6">
        <div className="max-w-2xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            
            <Card>
              <CardContent className="py-20 text-center">
                <QrCode className="w-16 h-16 text-muted-foreground mx-auto mb-6" />
                <h2 className="text-2xl font-bold mb-4">Private Record</h2>
                <p className="text-muted-foreground mb-6">
                  This record is private and cannot be shared via QR code.
                </p>
                <Button onClick={() => router.push(`/records/${recordId}`)}>
                  View Record Details
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-6 md:py-12 px-4 md:px-6">
      <div className="max-w-2xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-full">
                  <QrCode className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-xl">
                    {recordTitle || 'Trace Record'} - QR Code
                  </CardTitle>
                  <CardDescription>
                    Scan to view this blockchain traceability record
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {/* QR Code Display */}
                <div className="flex flex-col items-center space-y-6">
                  <div className="p-4 sm:p-6 bg-white rounded-2xl border-2 border-dashed border-gray-200 shadow-sm">
                    {loading ? (
                      <div className="w-72 h-72 sm:w-80 sm:h-80 md:w-96 md:h-96 flex items-center justify-center">
                        <Loader2 className="w-12 h-12 animate-spin text-muted-foreground" />
                      </div>
                    ) : (
                      <motion.img
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                        src={qrCodeDataUrl}
                        alt="QR Code for record"
                        className="w-72 h-72 sm:w-80 sm:h-80 md:w-96 md:h-96"
                      />
                    )}
                  </div>
                  
                  <div className="text-center space-y-2">
                    <p className="text-lg font-medium">Scan with any QR code reader</p>
                    <p className="text-sm text-muted-foreground max-w-md">
                      This QR code links directly to the blockchain record for easy verification and sharing
                    </p>
                    <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                      <span>Record ID:</span>
                      <code className="bg-muted px-2 py-1 rounded">{recordId}</code>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3"
                >
                  <Button
                    onClick={handleShare}
                    className="h-12 gap-3"
                    size="lg"
                  >
                    <Share2 className="w-5 h-5" />
                    Share QR Code
                  </Button>

                  <Button
                    onClick={fallbackCopyToClipboard}
                    variant="outline"
                    className="h-12 gap-3"
                    size="lg"
                  >
                    {copied ? (
                      <Check className="w-5 h-5 text-green-600" />
                    ) : (
                      <Copy className="w-5 h-5" />
                    )}
                    {copied ? 'Copied!' : 'Copy Link'}
                  </Button>

                  <Button
                    onClick={handleDownloadQR}
                    variant="outline"
                    className="h-12 gap-3"
                    size="lg"
                    disabled={loading || !qrCodeDataUrl}
                  >
                    <Download className="w-5 h-5" />
                    Download PNG
                  </Button>

                </motion.div>

                {/* URL Display */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  className="p-4 bg-muted/30 rounded-lg border"
                >
                  <p className="text-sm font-medium mb-2">Record URL:</p>
                  <div className="flex items-center gap-2">
                    <code className="flex-1 text-xs bg-background px-3 py-2 rounded border break-all">
                      {recordUrl}
                    </code>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={fallbackCopyToClipboard}
                      className="shrink-0"
                    >
                      {copied ? (
                        <Check className="w-4 h-4 text-green-600" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </motion.div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const { id } = context.query
  const baseUrl = process.env.BASE_URL || 'http://localhost:3000'
  
  if (!id || typeof id !== 'string') {
    return {
      props: {
        recordId: '',
        baseUrl,
        recordExists: false,
        isPublic: false
      }
    }
  }

  try {
    // Check if record exists and is public
    const response = await fetch(`${baseUrl}/api/records/${id}`)
    
    if (!response.ok) {
      return {
        props: {
          recordId: id,
          baseUrl,
          recordExists: false,
          isPublic: false
        }
      }
    }

    const data = await response.json()
    
    if (!data.success) {
      return {
        props: {
          recordId: id,
          baseUrl,
          recordExists: false,
          isPublic: false
        }
      }
    }

    return {
      props: {
        recordId: id,
        baseUrl,
        recordExists: true,
        isPublic: data.record.isPublic,
        recordTitle: data.record.json?.name || null
      }
    }
  } catch (error) {
    console.error('Error fetching record for QR page:', error)
    return {
      props: {
        recordId: id,
        baseUrl,
        recordExists: false,
        isPublic: false
      }
    }
  }
}