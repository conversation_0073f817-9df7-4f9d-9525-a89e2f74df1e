import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useWallet } from '@meshsdk/react'
import { useWalletVerification } from '@/lib/store/wallet-verification'
import { useNetworkStore } from '@/lib/store/network'
import { QRCodeShare } from '@/lib/components/QRCodeShare'
import { Button } from '@/lib/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/lib/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/lib/components/ui/table'
import { Badge } from '@/lib/components/ui/badge'
import { Switch } from '@/lib/components/ui/switch'
import { ExternalLink, Eye, Loader2, Wallet as WalletIcon, ChevronLeft, ChevronRight, AlertTriangle, RefreshCw } from 'lucide-react'
import { Alert, AlertDescription } from '@/lib/components/ui/alert'
import { toast } from 'sonner'
import { motion } from 'motion/react'
import { processWalletError } from '@/lib/utils/wallet-error-handler'

interface Record {
  id: string
  txHash: string
  ipfs: string | null
  json: any
  isPublic: boolean
  networkId: string
  winterId: string | null
  winterPublished: boolean
  winterStatus: string | null
  createdAt: string
}

interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

export default function Records() {
  const { wallet, connected } = useWallet()
  const { isVerified, walletAddress: verifiedWalletAddress, clearVerification } = useWalletVerification()
  const { selectedNetwork } = useNetworkStore()
  const isMainnet = selectedNetwork === true
  const [records, setRecords] = useState<Record[]>([])
  const [loading, setLoading] = useState(false)
  const [toggleLoading, setToggleLoading] = useState<string | null>(null)
  const [refreshingRecord, setRefreshingRecord] = useState<string | null>(null)
  const [lastRefreshTime, setLastRefreshTime] = useState<{ [key: string]: number }>({})
  const [pagination, setPagination] = useState<Pagination>({ page: 1, limit: 20, total: 0, totalPages: 0 })
  const [currentPage, setCurrentPage] = useState(1)

  useEffect(() => {
    if (!connected && isVerified) {
      
    }
  }, [connected, isVerified])

  useEffect(() => {
    const fetchRecords = async () => {
      if (!isVerified || !verifiedWalletAddress) {
        setRecords([])
        setPagination({ page: 1, limit: 20, total: 0, totalPages: 0 })
        return
      }

      setLoading(true)
      try {
        const params = new URLSearchParams({
          walletAddress: verifiedWalletAddress,
          page: currentPage.toString(),
          limit: '20'
        })
        
        const response = await fetch(`/api/records?${params}`)
        const data = await response.json()

        if (data.success) {
          setRecords(data.records || [])
          if (data.pagination) {
            setPagination(data.pagination)
          }
        } else {
          if (response.status === 404) {
            setRecords([])
            setPagination({ page: 1, limit: 20, total: 0, totalPages: 0 })
          } else {
            toast.error(data.error || 'Failed to fetch records')
          }
        }
      } catch (error) {
        toast.error('Failed to fetch records')
      } finally {
        setLoading(false)
      }
    }

    fetchRecords()
  }, [isVerified, verifiedWalletAddress, currentPage])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const toggleRecordVisibility = async (recordId: string, currentPublic: boolean) => {
    if (!wallet || !isVerified || !verifiedWalletAddress) {
      toast.error('Please connect and verify your wallet first')
      return
    }

    setToggleLoading(recordId)

    try {
      const nonceResponse = await fetch('/api/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          walletAddress: verifiedWalletAddress,
        }),
      })

      const nonceData = await nonceResponse.json()

      if (!nonceData.success) {
        throw new Error(nonceData.message || 'Failed to get nonce')
      }

      if (!nonceData.requiresReVerification) {
        throw new Error('Wallet verification required')
      }

      const nonce = nonceData.nonce

      const signatureResult = await wallet.signData(nonce, verifiedWalletAddress)
      const signatureString = JSON.stringify(signatureResult)

      const updateResponse = await fetch('/api/records/update-visibility', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recordId,
          isPublic: !currentPublic,
          walletAddress: verifiedWalletAddress,
          signature: signatureString,
          nonce,
        }),
      })

      const updateData = await updateResponse.json()

      if (!updateData.success) {
        throw new Error(updateData.error || 'Failed to update record visibility')
      }

      setRecords(prevRecords =>
        prevRecords.map(record =>
          record.id === recordId
            ? { ...record, isPublic: !currentPublic }
            : record
        )
      )

      toast.success(`Record is now ${!currentPublic ? 'public' : 'private'}`)

    } catch (error: any) {
      const walletErrorInfo = processWalletError(error, 'Record visibility update')
      toast.error(walletErrorInfo.message)
    } finally {
      setToggleLoading(null)
    }
  }

  const refreshRecordStatus = async (recordId: string) => {
    if (!isVerified || !verifiedWalletAddress) {
      toast.error('Please connect and verify your wallet first')
      return
    }

    const now = Date.now()
    const lastRefresh = lastRefreshTime[recordId] || 0
    if (now - lastRefresh < 3000) {
      return
    }

    setRefreshingRecord(recordId)
    setLastRefreshTime(prev => ({ ...prev, [recordId]: now }))

    try {
      const refreshResponse = await fetch('/api/records/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          recordId,
          walletAddress: verifiedWalletAddress,
        }),
      })

      const refreshData = await refreshResponse.json()

      if (!refreshData.success) {
        throw new Error(refreshData.message || 'Failed to refresh record status')
      }

      setRecords(prevRecords =>
        prevRecords.map(record =>
          record.id === recordId
            ? { 
                ...record, 
                winterStatus: refreshData.record.winterStatus,
                winterPublished: refreshData.record.winterPublished,
                txHash: refreshData.record.txHash
              }
            : record
        )
      )

      toast.success(`Record status updated: ${refreshData.record.winterStatus}`)

    } catch (error: any) {
      let errorMessage = 'Failed to refresh record status'
      if (error.message && typeof error.message === 'string') {
        errorMessage = error.message
      }
      toast.error(errorMessage)
    } finally {
      setRefreshingRecord(null)
    }
  }

  const recordNeedsRefresh = (record: Record): boolean => {
    return record.winterId !== null && record.winterStatus !== 'SUCCESS'
  }

  const getStatusBadge = (record: Record) => {
    if (!record.winterStatus) {
      return { variant: 'secondary', text: 'Unknown', needsRefresh: true }
    }
    
    switch (record.winterStatus) {
      case 'SUCCESS':
        return { variant: 'default', text: 'Success', needsRefresh: false }
      case 'PENDING':
        return { variant: 'secondary', text: 'Pending', needsRefresh: true }
      case 'FAILED':
        return { variant: 'destructive', text: 'Failed', needsRefresh: true }
      case 'NOT_FOUND':
        return { variant: 'destructive', text: 'Not Found', needsRefresh: true }
      default:
        return { variant: 'secondary', text: record.winterStatus, needsRefresh: true }
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getCardanoScanUrl = (txHash: string, networkId: string) => {
    const baseUrl = networkId === 'mainnet' 
      ? 'https://cardanoscan.io' 
      : 'https://preprod.cardanoscan.io'
    return `${baseUrl}/transaction/${txHash}`
  }

  const getIpfsUrl = (ipfsHash: string) => {
    return `https://ipfs.io/ipfs/${ipfsHash}`
  }

  if (!isMainnet) {
    return (
      <div className="min-h-screen py-6 md:py-12 px-4 md:px-6">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center py-20"
          >
            <AlertTriangle className="w-16 h-16 text-amber-600 mx-auto mb-6" />
            <h1 className="text-3xl font-bold mb-4">Preview Network</h1>
            <p className="text-muted-foreground mb-8 max-w-md mx-auto">
              Records are not stored on the preview network. To view and manage your records, please switch to mainnet.
            </p>
            <Alert className="max-w-md mx-auto border-amber-200 bg-amber-50 dark:bg-amber-950/20">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
              <AlertDescription>
                Preview network records are created for testing only and cannot be retrieved after creation. 
                Switch to mainnet to create permanent, retrievable records.
              </AlertDescription>
            </Alert>
          </motion.div>
        </div>
      </div>
    )
  }

  if (!isVerified) {
    return (
      <div className="min-h-screen py-6 md:py-12 px-4 md:px-6">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center py-20"
          >
            <WalletIcon className="w-16 h-16 text-muted-foreground mx-auto mb-6" />
            <h1 className="text-3xl font-bold mb-4">Your Records</h1>
            <p className="text-muted-foreground mb-8 max-w-md mx-auto">
              Connect and verify your wallet to view and manage your traceability records on the Cardano blockchain.
            </p>
            <div className="text-sm text-muted-foreground">
              Please connect your wallet and complete verification by signing the requested message.
            </div>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-6 md:py-12 px-4 md:px-6">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">Your Records</h1>
            <p className="text-muted-foreground">
              Manage your traceability records on the Cardano blockchain
            </p>
            {verifiedWalletAddress && (
              <div className="text-sm text-muted-foreground mt-2">
                Wallet: {verifiedWalletAddress.slice(0, 20)}...{verifiedWalletAddress.slice(-10)}
              </div>
            )}
          </div>

          {loading ? (
            <Card>
              <CardContent className="py-20">
                <div className="flex items-center justify-center">
                  <Loader2 className="w-8 h-8 animate-spin mr-3" />
                  <span>Loading your records...</span>
                </div>
              </CardContent>
            </Card>
          ) : records.length === 0 ? (
            <Card>
              <CardContent className="py-20 text-center">
                <div className="text-muted-foreground mb-4">
                  <Eye className="w-12 h-12 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No records found</h3>
                  <p>You haven&apos;t created any traceability records yet.</p>
                </div>
                <Button asChild>
                  <Link href="/create">Create Your First Record</Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                  <div>
                    <CardTitle>Your Records ({pagination.total})</CardTitle>
                    <CardDescription>
                      {loading ? 'Loading...' : `Showing ${records.length} of ${pagination.total} records`}
                    </CardDescription>
                  </div>
                  {loading && <Loader2 className="w-5 h-5 animate-spin" />}
                </div>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="min-w-[80px]">ID</TableHead>
                        <TableHead className="min-w-[120px]">TX Hash</TableHead>
                        <TableHead className="min-w-[100px]">IPFS</TableHead>
                        <TableHead className="min-w-[100px]">Status</TableHead>
                        <TableHead className="min-w-[100px]">Visibility</TableHead>
                        <TableHead className="min-w-[140px]">Actions</TableHead>
                        <TableHead className="min-w-[120px]">Created</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {records.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell className="font-mono text-sm">
                            {record.id}
                          </TableCell>
                          <TableCell>
                            <a
                              href={getCardanoScanUrl(record.txHash, record.networkId)}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center space-x-2 text-link hover:text-link hover:opacity-80 font-mono text-sm"
                            >
                              <span>{record.txHash.slice(0, 4)}..{record.txHash.slice(-4)}</span>
                              <ExternalLink className="w-3 h-3" />
                            </a>
                          </TableCell>
                          <TableCell>
                            {record.ipfs ? (
                              <a
                                href={getIpfsUrl(record.ipfs)}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center space-x-2 text-link hover:text-link hover:opacity-80 font-mono text-sm"
                              >
                                <span>{record.ipfs.slice(0, 4)}..{record.ipfs.slice(-4)}</span>
                                <ExternalLink className="w-3 h-3" />
                              </a>
                            ) : (
                              <span className="text-muted-foreground text-sm">-</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Badge 
                                variant={getStatusBadge(record).variant as any}
                                className="text-xs"
                              >
                                {getStatusBadge(record).text}
                              </Badge>
                              {recordNeedsRefresh(record) && (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  disabled={refreshingRecord === record.id}
                                  onClick={() => refreshRecordStatus(record.id)}
                                  className="p-1 h-6 w-6"
                                >
                                  {refreshingRecord === record.id ? (
                                    <Loader2 className="w-3 h-3 animate-spin" />
                                  ) : (
                                    <RefreshCw className="w-3 h-3" />
                                  )}
                                </Button>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Switch
                                checked={record.isPublic}
                                disabled={toggleLoading === record.id}
                                onCheckedChange={() => toggleRecordVisibility(record.id, record.isPublic)}
                              />
                              <span className="text-sm">
                                {toggleLoading === record.id ? (
                                  <div className="flex items-center">
                                    <Loader2 className="w-3 h-3 animate-spin mr-1" />
                                    Updating...
                                  </div>
                                ) : (
                                  record.isPublic ? 'Public' : 'Private'
                                )}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button size="sm" variant="default" asChild>
                                <a href={`/records/${record.id}`}>
                                  <Eye className="w-3 h-3 mr-1" />
                                  View
                                </a>
                              </Button>
                              {record.isPublic && (
                                <QRCodeShare 
                                  recordId={record.id}
                                  recordTitle={record.json?.name || 'Record'}
                                  isPublic={record.isPublic}
                                  compact={true}
                                />
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-sm">
                            {formatDate(record.createdAt)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {pagination.totalPages > 1 && (
                  <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6 pt-4 border-t">
                    <div className="text-sm text-muted-foreground">
                      Page {pagination.page} of {pagination.totalPages} 
                      ({pagination.total.toLocaleString()} total records)
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page === 1 || loading}
                      >
                        <ChevronLeft className="w-4 h-4" />
                        Previous
                      </Button>
                      
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                          const pageNum = Math.max(1, pagination.page - 2) + i
                          if (pageNum > pagination.totalPages) return null
                          
                          return (
                            <Button
                              key={pageNum}
                              variant={pageNum === pagination.page ? "default" : "outline"}
                              size="sm"
                              onClick={() => handlePageChange(pageNum)}
                              disabled={loading}
                              className="w-8 h-8 p-0"
                            >
                              {pageNum}
                            </Button>
                          )
                        })}
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page === pagination.totalPages || loading}
                      >
                        Next
                        <ChevronRight className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </motion.div>
      </div>
    </div>
  )
}