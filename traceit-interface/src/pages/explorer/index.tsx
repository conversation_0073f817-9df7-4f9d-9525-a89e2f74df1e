import { useState, useEffect } from 'react'
import { GetServerSideProps } from 'next'
import { 
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getSortedRowModel,
  SortingState,
} from '@tanstack/react-table'
import { But<PERSON> } from '@/lib/components/ui/button'
import { Input } from '@/lib/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/lib/components/ui/card'  
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/lib/components/ui/table'
import { Badge } from '@/lib/components/ui/badge'
import { 
  Search, 
  ExternalLink, 
  Eye, 
  Loader2, 
  Database,
  Calendar,
  ChevronLeft,
  ChevronRight,
  ArrowUpDown,
  Hash,
  User
} from 'lucide-react'
import { toast } from 'sonner'
import { motion } from 'motion/react'

interface ExplorerRecord {
  id: string
  txHash: string
  ipfs: string | null
  json: any
  networkId: string
  createdAt: string
  owner: {
    walletAddress: string
    walletName: string | null
  }
}

interface ExplorerStats {
  totalPublicRecords: number
  networks: { [key: string]: number }
}

interface ExplorerPageProps {
  initialRecords: ExplorerRecord[]
  initialStats: ExplorerStats
  initialPagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export default function ExplorerPage({ 
  initialRecords, 
  initialStats, 
  initialPagination 
}: ExplorerPageProps) {
  const [records, setRecords] = useState<ExplorerRecord[]>(initialRecords)
  const [stats, setStats] = useState<ExplorerStats>(initialStats)
  const [pagination, setPagination] = useState(initialPagination)
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [sorting, setSorting] = useState<SortingState>([])

  // Debounced search effect
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm !== '' || currentPage !== 1) {
        fetchRecords()
      }
    }, 500)

    return () => clearTimeout(delayedSearch)
  }, [searchTerm, currentPage])

  const fetchRecords = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        includeStats: 'true',
        sortBy: 'createdAt',
        sortOrder: 'desc'
      })

      if (searchTerm.trim()) {
        params.append('search', searchTerm.trim())
      }

      const response = await fetch(`/api/explorer?${params}`)
      const data = await response.json()

      if (data.success) {
        setRecords(data.records)
        setPagination(data.pagination)
        if (data.stats) {
          setStats(data.stats)
        }
      } else {
        toast.error('Failed to fetch records')
      }
    } catch (error) {
      console.error('Error fetching records:', error)
      toast.error('Failed to fetch records')
    } finally {
      setLoading(false)
    }
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Format wallet address for display
  const formatWalletAddress = (address: string) => {
    return `${address.slice(0, 8)}...${address.slice(-8)}`
  }

  // Generate external URLs
  const getCardanoScanUrl = (txHash: string, networkId: string) => {
    const baseUrl = networkId === 'mainnet' 
      ? 'https://cardanoscan.io' 
      : 'https://preprod.cardanoscan.io'
    return `${baseUrl}/transaction/${txHash}`
  }

  const getIpfsUrl = (ipfsHash: string) => {
    return `https://ipfs.io/ipfs/${ipfsHash}`
  }

  // Handle search input
  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  // Define columns for data table
  const columns: ColumnDef<ExplorerRecord>[] = [
    {
      accessorKey: 'id',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 p-0 font-medium hover:bg-transparent"
          >
            <Hash className="w-4 h-4 mr-2" />
            Record ID
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => (
        <div className="font-mono text-sm max-w-[120px] truncate" title={row.getValue('id')}>
          {row.getValue('id')}
        </div>
      ),
    },
    {
      accessorKey: 'txHash',
      header: () => (
        <div className="flex items-center">
          <ExternalLink className="w-4 h-4 mr-2" />
          TX Hash
        </div>
      ),
      cell: ({ row }) => {
        const txHash = row.getValue('txHash') as string
        const networkId = row.original.networkId
        return (
          <a
            href={getCardanoScanUrl(txHash, networkId)}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-2 text-link hover:text-link hover:opacity-80 font-mono text-sm"
          >
            <span>{txHash.slice(0, 6)}..{txHash.slice(-6)}</span>
            <ExternalLink className="w-3 h-3" />
          </a>
        )
      },
    },
    {
      accessorKey: 'ipfs',
      header: () => (
        <div className="flex items-center">
          <Database className="w-4 h-4 mr-2" />
          IPFS
        </div>
      ),
      cell: ({ row }) => {
        const ipfs = row.getValue('ipfs') as string | null
        return ipfs ? (
          <a
            href={getIpfsUrl(ipfs)}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-2 text-link hover:text-link hover:opacity-80 font-mono text-sm"
          >
            <span>{ipfs.slice(0, 6)}..{ipfs.slice(-6)}</span>
            <ExternalLink className="w-3 h-3" />
          </a>
        ) : (
          <span className="text-muted-foreground text-sm">-</span>
        )
      },
    },
    {
      accessorKey: 'owner',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 p-0 font-medium hover:bg-transparent"
          >
            <User className="w-4 h-4 mr-2" />
            Owner
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const owner = row.getValue('owner') as { walletAddress: string; walletName: string | null }
        return (
          <div className="font-mono text-xs">
            {formatWalletAddress(owner.walletAddress)}
          </div>
        )
      },
      sortingFn: (rowA, rowB) => {
        const ownerA = rowA.getValue('owner') as { walletAddress: string; walletName: string | null }
        const ownerB = rowB.getValue('owner') as { walletAddress: string; walletName: string | null }
        return ownerA.walletAddress.localeCompare(ownerB.walletAddress)
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        return (
          <Button size="sm" variant="outline" asChild>
            <a href={`/records/${row.original.id}`}>
              <Eye className="w-3 h-3 mr-1" />
              View
            </a>
          </Button>
        )
      },
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            className="h-8 p-0 font-medium hover:bg-transparent"
          >
            <Calendar className="w-4 h-4 mr-2" />
            Created
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        return (
          <div className="text-sm">
            {formatDate(row.getValue('createdAt'))}
          </div>
        )
      },
    },
  ]

  const table = useReactTable({
    data: records,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    state: {
      sorting,
    },
  })

  return (
    <div className="min-h-screen py-4 sm:py-6 md:py-12 px-4 sm:px-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Header */}
          <div className="mb-6 sm:mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold mb-2">Records Explorer</h1>
                <p className="text-muted-foreground">
                  Explore public traceability records on the Cardano blockchain
                </p>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Database className="w-4 h-4" />
                <span>{stats.totalPublicRecords.toLocaleString()} public records</span>
              </div>
            </div>

            {/* Search */}
            <Card>
              <CardContent className="p-6">
                <div className="relative max-w-2xl mx-auto">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                  <Input
                    placeholder="Search by record ID, owner address, TX hash, or IPFS..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-12 h-12 text-base"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Records Data Table */}
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <div>
                  <CardTitle>Public Records</CardTitle>
                  <CardDescription>
                    {loading ? 'Loading...' : `Showing ${records.length} of ${pagination.total} records`}
                  </CardDescription>
                </div>
                {loading && <Loader2 className="w-5 h-5 animate-spin" />}
              </div>
            </CardHeader>
            <CardContent>
              {records.length === 0 && !loading ? (
                <div className="text-center py-12">
                  <Database className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No records found</h3>
                  <p className="text-muted-foreground">
                    {searchTerm 
                      ? 'Try adjusting your search'
                      : 'No public records available yet'
                    }
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                          <TableRow key={headerGroup.id}>
                            {headerGroup.headers.map((header) => {
                              return (
                                <TableHead key={header.id}>
                                  {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                        header.column.columnDef.header,
                                        header.getContext()
                                      )}
                                </TableHead>
                              )
                            })}
                          </TableRow>
                        ))}
                      </TableHeader>
                      <TableBody>
                        {table.getRowModel().rows?.length ? (
                          table.getRowModel().rows.map((row) => (
                            <TableRow
                              key={row.id}
                              data-state={row.getIsSelected() && "selected"}
                              className="hover:bg-muted/50"
                            >
                              {row.getVisibleCells().map((cell) => (
                                <TableCell key={cell.id}>
                                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                </TableCell>
                              ))}
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={columns.length} className="h-24 text-center">
                              No results.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination */}
                  {pagination.totalPages > 1 && (
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 pt-4">
                      <div className="text-sm text-muted-foreground">
                        Page {pagination.page} of {pagination.totalPages} 
                        ({pagination.total.toLocaleString()} total records)
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page - 1)}
                          disabled={pagination.page === 1 || loading}
                        >
                          <ChevronLeft className="w-4 h-4" />
                          Previous
                        </Button>
                        
                        {/* Page numbers */}
                        <div className="flex items-center gap-1">
                          {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                            const pageNum = Math.max(1, pagination.page - 2) + i
                            if (pageNum > pagination.totalPages) return null
                            
                            return (
                              <Button
                                key={pageNum}
                                variant={pageNum === pagination.page ? "default" : "outline"}
                                size="sm"
                                onClick={() => handlePageChange(pageNum)}
                                disabled={loading}
                                className="w-8 h-8 p-0"
                              >
                                {pageNum}
                              </Button>
                            )
                          })}
                        </div>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page + 1)}
                          disabled={pagination.page === pagination.totalPages || loading}
                        >
                          Next
                          <ChevronRight className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const baseUrl = process.env.BASE_URL || 'http://localhost:3000'
  
  try {
    // Fetch initial data for the explorer
    const response = await fetch(`${baseUrl}/api/explorer?includeStats=true&limit=20&page=1`)
    const data = await response.json()

    if (data.success) {
      return {
        props: {
          initialRecords: data.records || [],
          initialStats: data.stats || { totalPublicRecords: 0, networks: {} },
          initialPagination: data.pagination || { page: 1, limit: 20, total: 0, totalPages: 0 }
        }
      }
    }
  } catch (error) {
    console.error('Error fetching initial explorer data:', error)
  }

  // Fallback props
  return {
    props: {
      initialRecords: [],
      initialStats: { totalPublicRecords: 0, networks: {} },
      initialPagination: { page: 1, limit: 20, total: 0, totalPages: 0 }
    }
  }
}