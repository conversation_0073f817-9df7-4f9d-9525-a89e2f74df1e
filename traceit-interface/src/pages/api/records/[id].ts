import type { NextApiRequest, NextApiResponse } from 'next';
import { db, records, wallets } from '@/lib/db';
import { eq } from 'drizzle-orm';

interface RecordDetailResponse {
  success: boolean;
  record?: {
    id: string;
    txHash: string;
    ipfs: string | null;
    json: any;
    isPublic: boolean;
    networkId: string;
    createdAt: Date;
    owner: {
      walletAddress: string;
      walletName: string | null;
    };
  };
  message?: string;
  error?: string;
  isPrivate?: boolean;
  isOwner?: boolean;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<RecordDetailResponse>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      error: `Method ${req.method} Not Allowed`
    });
  }

  try {
    const { id, walletAddress } = req.query;

    if (!id || typeof id !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Record ID is required'
      });
    }


    const result = await db
      .select({
        id: records.id,
        txHash: records.txHash,
        ipfs: records.ipfs,
        json: records.json,
        isPublic: records.isPublic,
        networkId: records.networkId,
        createdAt: records.createdAt,
        ownerWalletAddress: wallets.walletAddress,
        ownerWalletName: wallets.walletName,
      })
      .from(records)
      .innerJoin(wallets, eq(records.walletId, wallets.id))
      .where(eq(records.id, id))
      .limit(1);

    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'This record does not exist'
      });
    }

    const recordData = result[0];

    const isOwner = walletAddress && 
                   typeof walletAddress === 'string' && 
                   recordData.ownerWalletAddress === walletAddress;

    if (!recordData.isPublic && !isOwner) {
      return res.status(403).json({
        success: false,
        message: 'This record is private',
        isPrivate: true
      });
    }


    return res.status(200).json({
      success: true,
      record: {
        id: recordData.id,
        txHash: recordData.txHash,
        ipfs: recordData.ipfs,
        json: recordData.json,
        isPublic: recordData.isPublic,
        networkId: recordData.networkId,
        createdAt: recordData.createdAt,
        owner: {
          walletAddress: recordData.ownerWalletAddress,
          walletName: recordData.ownerWalletName,
        }
      },
      isOwner: isOwner || false
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}