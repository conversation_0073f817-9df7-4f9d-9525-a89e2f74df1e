import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { db, records, wallets } from '@/lib/db';
import { eq, and } from 'drizzle-orm';
import { ApiErrorHandler } from '@/lib/utils/api-errors';

const getWinterBaseUrl = (isMainnet: boolean): string => {
  return isMainnet 
    ? process.env.MAINNET_WINTER_URL || ''
    : process.env.PREVIEW_WINTER_URL || '';
};

interface RefreshRecordRequest {
  recordId: string;
  walletAddress: string;
}

interface WinterCheckResponse {
  id: string;
  type: string;
  status: string;
  error: string;
  txid?: string;
  additionalInfo?: {
    tokenName: string;
    metadataReference: string;
  };
}

interface RefreshRecordResponse {
  success: boolean;
  message: string;
  record?: {
    id: string;
    winterStatus: string | null;
    winterPublished: boolean;
    txHash: string;
  };
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<RefreshRecordResponse>
) {
  if (req.method !== 'POST') {
    return ApiErrorHandler.methodNotAllowed(res, ['POST']);
  }

  try {
    const { recordId, walletAddress } = req.body as RefreshRecordRequest;

    if (!recordId || !walletAddress) {
      return ApiErrorHandler.badRequest(res, 'Record ID and wallet address are required');
    }


    const wallet = await db
      .select()
      .from(wallets)
      .where(eq(wallets.walletAddress, walletAddress))
      .limit(1);

    if (wallet.length === 0) {
      return ApiErrorHandler.notFound(res, 'Wallet not found');
    }

    const record = await db
      .select()
      .from(records)
      .where(and(
        eq(records.id, recordId),
        eq(records.walletId, wallet[0].id)
      ))
      .limit(1);

    if (record.length === 0) {
      return ApiErrorHandler.notFound(res, 'Record not found or access denied');
    }

    const recordData = record[0];
    
    if (!recordData.winterId) {
      return ApiErrorHandler.badRequest(res, 'Record does not have a Winter ID to validate');
    }

    const isMainnet = recordData.networkId === 'mainnet';
    const winterBaseUrl = getWinterBaseUrl(isMainnet);
    
    if (!winterBaseUrl) {
      return ApiErrorHandler.serviceUnavailable(res, `Winter API URL not configured for ${isMainnet ? 'mainnet' : 'preview'}`);
    }


    try {
      const checkResponse = await axios.get<WinterCheckResponse>(`${winterBaseUrl}/check/${recordData.winterId}`, {
        headers: {
          'Accept': 'application/json'
        },
        timeout: 10000
      });

      const winterData = checkResponse.data;

      const updatedRecord = await db
        .update(records)
        .set({
          winterStatus: winterData.status,
          winterPublished: winterData.status === 'SUCCESS',
          ...(winterData.txid && { txHash: winterData.txid })
        })
        .where(eq(records.id, recordId))
        .returning({
          id: records.id,
          winterStatus: records.winterStatus,
          winterPublished: records.winterPublished,
          txHash: records.txHash
        });


      return res.status(200).json({
        success: true,
        message: `Record status updated to ${winterData.status}`,
        record: updatedRecord[0]
      });

    } catch (winterError: any) {
      
      if (winterError.response) {
        if (winterError.response.status === 404) {
          await db
            .update(records)
            .set({
              winterStatus: 'NOT_FOUND'
            })
            .where(eq(records.id, recordId));

          return ApiErrorHandler.notFound(res, 'Winter ID not found in Winter protocol');
        } else if (winterError.response.status >= 500) {
          return ApiErrorHandler.serviceUnavailable(res, 'Winter protocol service is temporarily unavailable');
        }
      }

      if (winterError.code === 'ECONNABORTED' || winterError.code === 'ENOTFOUND') {
        return ApiErrorHandler.serviceUnavailable(res, 'Unable to connect to Winter protocol. Please try again later.');
      }

      return ApiErrorHandler.internalError(res, 'Failed to validate record with Winter protocol');
    }

  } catch (error: any) {
    return ApiErrorHandler.internalError(res, 'Failed to refresh record status');
  }
}