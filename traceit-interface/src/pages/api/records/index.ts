import type { NextApiRequest, NextApiResponse } from 'next';
import { db, records, wallets } from '@/lib/db';
import { eq, desc, count } from 'drizzle-orm';

interface RecordsResponse {
  success: boolean;
  records?: Array<{
    id: string;
    txHash: string;
    ipfs: string | null;
    json: any;
    isPublic: boolean;
    networkId: string;
    createdAt: Date;
  }>;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message?: string;
  error?: string;
}

interface RecordsRequestBody {
  walletAddress?: string;
}

const validatePage = (page: any): number => {
  const parsed = parseInt(page);
  return isNaN(parsed) || parsed < 1 ? 1 : Math.min(parsed, 1000);
};

const validateLimit = (limit: any): number => {
  const parsed = parseInt(limit);
  return isNaN(parsed) || parsed < 1 ? 20 : Math.min(parsed, 100);
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<RecordsResponse>
) {
  if (req.method === 'GET') {
    const { walletAddress } = req.query;

    if (!walletAddress || typeof walletAddress !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Wallet address is required as query parameter'
      });
    }

    const page = validatePage(req.query.page);
    const limit = validateLimit(req.query.limit);

    try {

      const wallet = await db
        .select()
        .from(wallets)
        .where(eq(wallets.walletAddress, walletAddress))
        .limit(1);

      if (wallet.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Wallet not found'
        });
      }

      const [totalResult] = await db
        .select({ count: count() })
        .from(records)
        .where(eq(records.walletId, wallet[0].id));

      const total = totalResult.count;
      const totalPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const userRecords = await db
        .select({
          id: records.id,
          txHash: records.txHash,
          ipfs: records.ipfs,
          json: records.json,
          isPublic: records.isPublic,
          networkId: records.networkId,
          winterId: records.winterId,
          winterPublished: records.winterPublished,
          winterStatus: records.winterStatus,
          createdAt: records.createdAt,
        })
        .from(records)
        .where(eq(records.walletId, wallet[0].id))
        .orderBy(desc(records.createdAt))
        .limit(limit)
        .offset(offset);


      return res.status(200).json({
        success: true,
        records: userRecords,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      });

    } catch (error) {
      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  if (req.method === 'POST') {
    const { walletAddress }: RecordsRequestBody = req.body;

    if (!walletAddress) {
      return res.status(400).json({
        success: false,
        error: 'Wallet address is required'
      });
    }

    try {

      const wallet = await db
        .select()
        .from(wallets)
        .where(eq(wallets.walletAddress, walletAddress))
        .limit(1);

      if (wallet.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Wallet not found'
        });
      }

      const userRecords = await db
        .select({
          id: records.id,
          txHash: records.txHash,
          ipfs: records.ipfs,
          json: records.json,
          isPublic: records.isPublic,
          networkId: records.networkId,
          createdAt: records.createdAt,
        })
        .from(records)
        .where(eq(records.walletId, wallet[0].id))
        .orderBy(desc(records.createdAt));


      return res.status(200).json({
        success: true,
        records: userRecords
      });

    } catch (error) {
      return res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  res.setHeader('Allow', ['GET', 'POST']);
  return res.status(405).json({
    success: false,
    error: `Method ${req.method} Not Allowed`
  });
}