import type { NextApiRequest, NextApiResponse } from 'next';
import { db, records, wallets } from '@/lib/db';
import { eq, and } from 'drizzle-orm';

interface UpdateVisibilityResponse {
  success: boolean;
  message?: string;
  error?: string;
  record?: {
    id: string;
    isPublic: boolean;
  };
}

interface UpdateVisibilityRequestBody {
  recordId: string;
  isPublic: boolean;
  walletAddress: string;
  signature: string;
  nonce: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<UpdateVisibilityResponse>
) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({
      success: false,
      error: `Method ${req.method} Not Allowed`
    });
  }

  try {
    const { recordId, isPublic, walletAddress, signature, nonce }: UpdateVisibilityRequestBody = req.body;

    if (!recordId || typeof isPublic !== 'boolean' || !walletAddress || !signature || !nonce) {
      return res.status(400).json({
        success: false,
        error: 'Record ID, visibility status, wallet address, signature, and nonce are required'
      });
    }


    const wallet = await db
      .select()
      .from(wallets)
      .where(eq(wallets.walletAddress, walletAddress))
      .limit(1);

    if (wallet.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Wallet not found'
      });
    }

    const walletRecord = wallet[0];

    if (!walletRecord.isVerified || !walletRecord.nonce) {
      return res.status(403).json({
        success: false,
        error: 'Wallet not verified'
      });
    }

    if (walletRecord.nonce !== nonce) {
      return res.status(403).json({
        success: false,
        error: 'Invalid nonce'
      });
    }

    if (!walletRecord.walletSignature || walletRecord.walletSignature !== signature) {
      return res.status(403).json({
        success: false,
        error: 'Invalid signature - please reconnect your wallet'
      });
    }

    const record = await db
      .select()
      .from(records)
      .where(
        and(
          eq(records.id, recordId),
          eq(records.walletId, walletRecord.id)
        )
      )
      .limit(1);

    if (record.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Record not found or you do not own this record'
      });
    }

    const updatedRecord = await db
      .update(records)
      .set({
        isPublic,
      })
      .where(eq(records.id, recordId))
      .returning({
        id: records.id,
        isPublic: records.isPublic,
      });


    return res.status(200).json({
      success: true,
      message: `Record visibility updated to ${isPublic ? 'public' : 'private'}`,
      record: updatedRecord[0]
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
}