import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { db, records } from '@/lib/db';
import { eq } from 'drizzle-orm';

const getWinterBaseUrl = (isMainnet: boolean): string => {
  return isMainnet 
    ? process.env.MAINNET_WINTER_URL || ''
    : process.env.PREVIEW_WINTER_URL || '';
};

interface ValidateTokenizeRequest {
  winterId: string;
  isMainnet: boolean;
}

interface WinterCheckResponse {
  id: string;
  type: string;
  status: string;
  error: string;
  txid?: string;
  additionalInfo?: {
    tokenName: string;
    metadataReference: string;
  };
}

interface ValidateTokenizeResponse {
  success: boolean;
  message: string;
  status?: string;
  txHash?: string;
  metadataReference?: string;
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ValidateTokenizeResponse>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  try {
    const { winterId, isMainnet } = req.body as ValidateTokenizeRequest;

    if (!winterId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Winter ID is required' 
      });
    }

    if (typeof isMainnet !== 'boolean') {
      return res.status(400).json({ 
        success: false, 
        message: 'isMainnet flag is required' 
      });
    }

    const winterBaseUrl = getWinterBaseUrl(isMainnet);
    if (!winterBaseUrl) {
      return res.status(500).json({ 
        success: false, 
        message: `Winter API URL not configured for ${isMainnet ? 'mainnet' : 'preview'}` 
      });
    }

    const checkResponse = await axios.get<WinterCheckResponse>(`${winterBaseUrl}/check/${winterId}`, {
      headers: {
        'Accept': 'application/json'
      }
    });

    const winterData = checkResponse.data;
    if (isMainnet && winterData.txid && winterData.txid !== 'No tx id.' && winterData.txid !== '') {
      const existingRecord = await db
        .select()
        .from(records)
        .where(eq(records.winterId, winterId))
        .limit(1);

      if (existingRecord.length > 0) {
        await db
          .update(records)
          .set({
            txHash: winterData.txid,
            winterStatus: winterData.status,
            winterPublished: winterData.status === 'SUCCESS'
          })
          .where(eq(records.winterId, winterId));

      } else {
      }
    }

    const metadataReference = winterData.additionalInfo?.metadataReference;
    const ipfsHash = metadataReference?.replace('ipfs://', '');

    const response: ValidateTokenizeResponse = {
      success: true,
      message: 'Validation successful',
      status: winterData.status
    };
    
    if (winterData.txid && winterData.txid !== 'No tx id.' && winterData.txid !== '') {
      response.txHash = winterData.txid;
    }
    
    if (metadataReference) {
      response.metadataReference = metadataReference;
    }
    
    return res.status(200).json(response);

  } catch (error: any) {
    if (error.response) {
      if (error.response.status === 404) {
        return res.status(404).json({
          success: false,
          message: 'Winter ID not found',
          status: 'NOT_FOUND'
        });
      }

      return res.status(error.response.status).json({
        success: false,
        message: 'Winter API error',
        error: error.response.data?.message || error.response.data?.error || 'Unknown error'
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Failed to validate tokenized record',
      error: error.message
    });
  }
}