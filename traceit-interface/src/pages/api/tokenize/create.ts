import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { db, records, wallets } from '@/lib/db';
import { eq, and } from 'drizzle-orm';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, USER_FRIENDLY_MESSAGES } from '@/lib/utils/api-errors';

const generateSmallHash = (): string => {
  const chars = '0123456789abcdefghijklmnopqrstuvwxyz';
  return Array.from({ length: 5 }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
};

const generateShortId = (): string => {
  const chars = '0123456789abcdefghijklmnopqrstuvwxyz';
  const part1 = Array.from({ length: 4 }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
  const part2 = Array.from({ length: 4 }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
  return `${part1}-${part2}`;
};

const getWinterBaseUrl = (isMainnet: boolean): string => {
  return isMainnet 
    ? process.env.MAINNET_WINTER_URL || ''
    : process.env.PREVIEW_WINTER_URL || '';
};

const createRecordWithFallback = async (recordData: any, maxRetries: number = 5) => {
  let attempts = 0;
  
  while (attempts < maxRetries) {
    try {
      const recordId = generateShortId();
      const result = await db.insert(records).values({
        ...recordData,
        id: recordId
      }).returning();
      
      return result[0];
    } catch (error: any) {
      attempts++;
      
      if (error.code === '23505') {
        if (error.constraint?.includes('records_pkey')) {
          if (attempts >= maxRetries) {
            throw new Error('Failed to generate unique ID after multiple attempts');
          }
          continue;
        } else if (error.constraint?.includes('ipfs_unique')) {
          const existingRecord = await db
            .select()
            .from(records)
            .where(eq(records.ipfs, recordData.ipfs))
            .limit(1);
          
          if (existingRecord.length > 0) {
            return existingRecord[0];
          } else {
            throw new Error('IPFS duplicate detected but could not find existing record');
          }
        } else {
          throw error;
        }
      } else {
        throw error;
      }
    }
  }
  
  throw new Error('Failed to create record after maximum retries');
};

interface CreateTokenizeRequest {
  jsonSchema: any;
  walletAddress?: string;
  isPublic?: boolean;
  isMainnet: boolean;
  paymentHash?: string;
}

interface CreateTokenizeResponse {
  success: boolean;
  message: string;
  id?: string;
  recordId?: string;
  tokenName?: string;
  metadataReference?: string;
  error?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<CreateTokenizeResponse>
) {
  if (req.method !== 'POST') {
    return ApiErrorHandler.methodNotAllowed(res, ['POST']);
  }

  try {
    const { jsonSchema, walletAddress, isPublic, isMainnet, paymentHash } = req.body as CreateTokenizeRequest;

    if (!jsonSchema) {
      return ApiErrorHandler.badRequest(res, 'Record data is required to create a trace record');
    }

    if (isMainnet) {
      if (!walletAddress) {
        return ApiErrorHandler.badRequest(res, USER_FRIENDLY_MESSAGES.WALLET_ADDRESS_REQUIRED);
      }
      if (typeof isPublic !== 'boolean') {
        return ApiErrorHandler.badRequest(res, 'Privacy setting is required for mainnet records');
      }
      if (!paymentHash) {
        return ApiErrorHandler.badRequest(res, USER_FRIENDLY_MESSAGES.PAYMENT_REQUIRED);
      }
    }

    const winterBaseUrl = getWinterBaseUrl(isMainnet);
    if (!winterBaseUrl) {
      return ApiErrorHandler.serviceUnavailable(res, `Winter Protocol service is not configured for ${isMainnet ? 'mainnet' : 'preview'} network`);
    }

    let ipfsResponse;
    let ipfsHash;
    const maxRetries = 3;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        ipfsResponse = await axios.post(`${winterBaseUrl}/ipfs`, jsonSchema, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 30000
        });

        ipfsHash = ipfsResponse.data.hash || ipfsResponse.data.ipfsHash || ipfsResponse.data.cid;
        if (!ipfsHash) {
          throw new Error('Failed to get IPFS hash from response');
        }

        if (isMainnet && walletAddress) {
          const existingRecord = await db
            .select()
            .from(records)
            .where(eq(records.ipfs, ipfsHash))
            .limit(1);

          if (existingRecord.length > 0) {
            return res.status(200).json({
              success: true,
              message: 'Record already exists with this content',
              id: existingRecord[0].winterId || 'existing-record',
              recordId: existingRecord[0].id,
              tokenName: `TRC_IT_${generateSmallHash()}`,
              metadataReference: `ipfs://${ipfsHash}`
            });
          }
        }
        
        break;
        
      } catch (error: any) {
        if ((error.code === 'ECONNABORTED' || error.response?.status === 500 || error.code === 'ECONNRESET') && attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 3000));
          continue;
        }
        
        if (attempt === maxRetries) {
          if (error.code === 'ECONNABORTED' || error.code === 'ECONNRESET') {
            throw new Error('IPFS service is unreachable. Please check your connection and try again.');
          }
          
          if (error.response?.status === 500) {
            ipfsHash = 'temp_hash_retry_' + Date.now();
            break;
          }
        }
        
        throw error;
      }
    }

    const smallHash = generateSmallHash();
    const tokenName = `TRC_IT_${smallHash}`;
    const metadataReference = `ipfs://${ipfsHash}`;

    const tokenizeResponse = await axios.post(`${winterBaseUrl}/palmyra/tokenizeCommodity`, {
      tokenName,
      metadataReference
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const winterId = tokenizeResponse.data.id;
    if (!winterId) {
      throw new Error('Failed to get Winter ID from tokenize response');
    }

    if (isMainnet) {
      let wallet = await db
        .select()
        .from(wallets)
        .where(eq(wallets.walletAddress, walletAddress!))
        .limit(1);

      if (wallet.length === 0) {
        const newWallet = await db
          .insert(wallets)
          .values({
            walletAddress: walletAddress!,
            walletName: null,
            role: 'user',
            isVerified: false
          })
          .returning();
        wallet = newWallet;
      }

      const newRecord = await createRecordWithFallback({
        walletId: wallet[0].id,
        txHash: '',
        paymentHash: paymentHash!,
        ipfs: ipfsHash,
        json: jsonSchema,
        isPublic: isPublic!,
        networkId: 'mainnet',
        winterId: winterId,
        winterPublished: false,
        winterStatus: 'PENDING'
      });

      return res.status(200).json({
        success: true,
        message: tokenizeResponse.data.message || 'Token created successfully',
        id: winterId,
        recordId: newRecord.id,
        tokenName,
        metadataReference
      });
    }

    return res.status(200).json({
      success: true,
      message: tokenizeResponse.data.message || 'Token created successfully',
      id: winterId,
      tokenName,
      metadataReference
    });

  } catch (error: any) {
    if (error.response) {
      return ApiErrorHandler.externalApiError(res, 'Winter Protocol', error);
    }

    if (error.code && error.code.startsWith('23')) {
      if (error.code === '23505') {
        if (error.constraint?.includes('ipfs')) {
          try {
            const existingRecord = await db
              .select()
              .from(records)
              .where(eq(records.ipfs, error.detail?.match(/\(ipfs\)=\(([^)]+)\)/)?.[1] || ''))
              .limit(1);
            
            if (existingRecord.length > 0) {
              return res.status(200).json({
                success: true,
                message: 'Record already exists with this content',
                id: existingRecord[0].winterId || 'existing-record',
                recordId: existingRecord[0].id,
                tokenName: `TRC_IT_${generateSmallHash()}`,
                metadataReference: `ipfs://${existingRecord[0].ipfs}`
              });
            }
          } catch (lookupError) {
          }
        }
        
        if (error.constraint?.includes('winter_id')) {
          return ApiErrorHandler.conflict(res, 'Winter ID already exists. Please try again.');
        }
        
        if (error.constraint?.includes('tx_hash')) {
          return ApiErrorHandler.conflict(res, 'Transaction hash already exists.');
        }
      }
      
      return ApiErrorHandler.databaseError(res, error);
    }

    if (error.message) {
      if (error.message.includes('IPFS')) {
        return ApiErrorHandler.serviceUnavailable(res, USER_FRIENDLY_MESSAGES.IPFS_UPLOAD_FAILED);
      }
      if (error.message.includes('Winter')) {
        return ApiErrorHandler.serviceUnavailable(res, USER_FRIENDLY_MESSAGES.WINTER_API_ERROR);
      }
      if (error.message.includes('unique ID')) {
        return ApiErrorHandler.conflict(res, 'Record ID collision occurred. Please try again.');
      }
    }

    return ApiErrorHandler.internalError(res, USER_FRIENDLY_MESSAGES.RECORD_CREATION_FAILED);
  }
}