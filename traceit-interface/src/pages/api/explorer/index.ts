import type { NextApiRequest, NextApiResponse } from 'next';
import { db, records, wallets } from '@/lib/db';
import { eq, and, or, like, desc, asc, gte, lte, count, sql } from 'drizzle-orm';

interface ExplorerRecord {
  id: string;
  txHash: string;
  ipfs: string | null;
  json: any;
  networkId: string;
  createdAt: string;
  owner: {
    walletAddress: string;
    walletName: string | null;
  };
}

interface ExplorerResponse {
  success: boolean;
  records: ExplorerRecord[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  stats?: {
    totalPublicRecords: number;
    networks: { [key: string]: number };
  };
  message?: string;
}

const validatePage = (page: any): number => {
  const parsed = parseInt(page);
  return isNaN(parsed) || parsed < 1 ? 1 : Math.min(parsed, 1000);
};

const validateLimit = (limit: any): number => {
  const parsed = parseInt(limit);
  return isNaN(parsed) || parsed < 1 ? 20 : Math.min(parsed, 100);
};

const validateSearch = (search: any): string => {
  if (typeof search !== 'string') return '';
  return search.replace(/[^a-zA-Z0-9\s\-_.,!?@#$%&*()]/g, '').substring(0, 100);
};

const validateNetwork = (network: any): string | null => {
  if (typeof network !== 'string') return null;
  const validNetworks = ['mainnet', 'preview'];
  return validNetworks.includes(network) ? network : null;
};

const validateDateRange = (date: any): Date | null => {
  if (typeof date !== 'string') return null;
  const parsed = new Date(date);
  return isNaN(parsed.getTime()) ? null : parsed;
};

const validateSortBy = (sortBy: any): string => {
  const validSorts = ['createdAt', 'id', 'networkId'];
  return validSorts.includes(sortBy) ? sortBy : 'createdAt';
};

const validateSortOrder = (order: any): 'asc' | 'desc' => {
  return order === 'asc' ? 'asc' : 'desc';
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ExplorerResponse>
) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ 
      success: false, 
      records: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
      message: `Method ${req.method} Not Allowed` 
    });
  }

  try {
    // Validate and sanitize inputs
    const page = validatePage(req.query.page);
    const limit = validateLimit(req.query.limit);
    const search = validateSearch(req.query.search);
    const network = validateNetwork(req.query.network);
    const fromDate = validateDateRange(req.query.fromDate);
    const toDate = validateDateRange(req.query.toDate);
    const sortBy = validateSortBy(req.query.sortBy);
    const sortOrder = validateSortOrder(req.query.sortOrder);
    const includeStats = req.query.includeStats === 'true';


    const conditions = [
      eq(records.isPublic, true)
    ];

    if (search) {
      conditions.push(
        or(
          like(records.id, `%${search}%`),
          like(records.txHash, `%${search}%`),
          sql`CAST(${records.json} AS TEXT) LIKE ${`%${search}%`}`
        )!
      );
    }

    if (network) {
      conditions.push(eq(records.networkId, network));
    }

    if (fromDate) {
      conditions.push(gte(records.createdAt, fromDate));
    }
    if (toDate) {
      conditions.push(lte(records.createdAt, toDate));
    }

    const whereClause = and(...conditions);

    const [totalResult] = await db
      .select({ count: count() })
      .from(records)
      .leftJoin(wallets, eq(records.walletId, wallets.id))
      .where(whereClause);

    const total = totalResult.count;
    const totalPages = Math.ceil(total / limit);
    const offset = (page - 1) * limit;
    const recordsWithOwners = await db
      .select({
        id: records.id,
        txHash: records.txHash,
        ipfs: records.ipfs,
        json: records.json,
        networkId: records.networkId,
        createdAt: records.createdAt,
        ownerWalletAddress: wallets.walletAddress,
        ownerWalletName: wallets.walletName,
      })
      .from(records)
      .leftJoin(wallets, eq(records.walletId, wallets.id))
      .where(whereClause)
      .orderBy(
        sortOrder === 'desc' 
          ? desc(sortBy === 'createdAt' ? records.createdAt : records.id)
          : asc(sortBy === 'createdAt' ? records.createdAt : records.id)
      )
      .limit(limit)
      .offset(offset);

    const formattedRecords: ExplorerRecord[] = recordsWithOwners.map(record => ({
      id: record.id,
      txHash: record.txHash,
      ipfs: record.ipfs,
      json: record.json,
      networkId: record.networkId,
      createdAt: record.createdAt.toISOString(),
      owner: {
        walletAddress: record.ownerWalletAddress || 'Unknown',
        walletName: record.ownerWalletName || null,
      }
    }));

    let stats;
    if (includeStats) {
      const [totalPublicResult] = await db
        .select({ count: count() })
        .from(records)
        .where(eq(records.isPublic, true));

      const networkStats = await db
        .select({
          networkId: records.networkId,
          count: count()
        })
        .from(records)
        .where(eq(records.isPublic, true))
        .groupBy(records.networkId);

      stats = {
        totalPublicRecords: totalPublicResult.count,
        networks: networkStats.reduce((acc, stat) => {
          acc[stat.networkId] = stat.count;
          return acc;
        }, {} as { [key: string]: number })
      };
    }

    return res.status(200).json({
      success: true,
      records: formattedRecords,
      pagination: {
        page,
        limit,
        total,
        totalPages
      },
      stats
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      records: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
      message: 'Internal server error'
    });
  }
}