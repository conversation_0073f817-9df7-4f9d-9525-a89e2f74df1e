import type { NextApiRequest, NextApiResponse } from 'next';
import { db, wallets, type NewWallet } from '@/lib/db';
import { eq, and } from 'drizzle-orm';
import crypto from 'crypto';

interface ValidateResponse {
  success: boolean;
  nonce?: string;
  expiresAt?: Date;
  message?: string;
  walletId?: string;
  isVerified?: boolean;
  requiresReVerification?: boolean;
}

interface ValidateRequestBody {
  walletAddress?: string;
  signature?: string;
  nonce?: string;
  walletName?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ValidateResponse>
) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ success: false, message: `Method ${req.method} Not Allowed` });
  }

  try {
    const { walletAddress, signature, nonce, walletName }: ValidateRequestBody = req.body;

    if (!signature && !nonce) {
      if (!walletAddress) {
        return res.status(400).json({ 
          success: false, 
          message: 'Wallet address is required' 
        });
      }

      
      const existingWallet = await db
        .select()
        .from(wallets)
        .where(eq(wallets.walletAddress, walletAddress))
        .limit(1);

      if (existingWallet.length > 0) {
        const wallet = existingWallet[0];
        
        if (wallet.isVerified && wallet.nonce) {
          
          return res.status(200).json({
            success: true,
            nonce: wallet.nonce,
            requiresReVerification: true,
            message: 'Please sign the nonce to confirm wallet ownership'
          });
        }

        if (wallet.nonce) {
          return res.status(200).json({
            success: true,
            nonce: wallet.nonce,
            message: 'Use existing nonce to sign'
          });
        }
      }

      const newNonce = crypto.randomBytes(32).toString('hex');
      
      if (existingWallet.length > 0) {
        await db
          .update(wallets)
          .set({
            nonce: newNonce,
            lastActive: new Date(),
            ...(walletName && { walletName })
          })
          .where(eq(wallets.walletAddress, walletAddress));
      } else {
        const newWallet: NewWallet = {
          walletAddress,
          walletName: walletName || null,
          nonce: newNonce,
          isVerified: false,
          role: 'user'
        };

        await db.insert(wallets).values(newWallet);
      }

      return res.status(200).json({
        success: true,
        nonce: newNonce,
        message: 'Sign this nonce with your wallet to prove ownership'
      });
    }

    if (!walletAddress || !signature || !nonce) {
      return res.status(400).json({
        success: false,
        message: 'Wallet address, signature, and nonce are required for verification'
      });
    }

    const walletRecord = await db
      .select()
      .from(wallets)
      .where(
        and(
          eq(wallets.walletAddress, walletAddress),
          eq(wallets.nonce, nonce)
        )
      )
      .limit(1);

    if (walletRecord.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Wallet not found or invalid nonce'
      });
    }

    const wallet = walletRecord[0];

    
    const isSignatureValid = signature.length > 0;

    if (!isSignatureValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid signature'
      });
    }

    if (wallet.isVerified && wallet.walletSignature) {
      if (signature !== wallet.walletSignature) {
        return res.status(400).json({
          success: false,
          message: 'Invalid signature - wallet authentication failed'
        });
      }
      
      await db
        .update(wallets)
        .set({
          lastActive: new Date(),
        })
        .where(eq(wallets.id, wallet.id));

      return res.status(200).json({
        success: true,
        message: 'Wallet re-authenticated successfully',
        walletId: wallet.walletName || 'unknown',
        isVerified: true
      });
    }

    
    const updatedWallet = await db
      .update(wallets)
      .set({
        walletSignature: signature,
        isVerified: true,
        lastActive: new Date(),
        ...(walletName && { walletName })
      })
      .where(eq(wallets.id, wallet.id))
      .returning();

    return res.status(200).json({
      success: true,
      message: 'Wallet ownership verified and signature stored for security',
      walletId: updatedWallet[0].walletName || 'unknown',
      isVerified: true
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}