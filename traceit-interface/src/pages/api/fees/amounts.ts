import type { NextApiRequest, NextApiResponse } from 'next';
import { ApiErrorHandler } from '@/lib/utils/api-errors';

interface FeeAmountsResponse {
  success: boolean;
  message?: string;
  adaFeeAmount?: number;
  palmFeeAmount?: number;
  minAdaFeeAmount?: number;
  totalAdaFeeAmount?: number; // ADA fee + min ADA fee
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<FeeAmountsResponse>
) {
  if (req.method !== 'GET') {
    return ApiErrorHandler.methodNotAllowed(res, ['GET']);
  }

  try {
    const adaFeeAmount = Number(process.env.ADA_FEE_AMOUNT || 0);
    const palmFeeAmount = Number(process.env.PALM_FEE_AMOUNT || 0);
    const minAdaFeeAmount = Number(process.env.MIN_ADA_FEE_AMOUNT || 0);
    const totalAdaFeeAmount = adaFeeAmount + minAdaFeeAmount;

    return res.status(200).json({
      success: true,
      message: 'Fee amounts retrieved successfully',
      adaFeeAmount,
      palmFeeAmount,
      minAdaFeeAmount,
      totalAdaFeeAmount
    });

  } catch (error: any) {
    return ApiErrorHandler.internalError(res, 'Failed to retrieve fee amounts');
  }
}