import type { NextApiRequest, NextApiResponse } from 'next';
import { <PERSON><PERSON><PERSON>t<PERSON><PERSON>ider, MeshTxBuilder } from "@meshsdk/core";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, USER_FRIENDLY_MESSAGES } from '@/lib/utils/api-errors';

const BLOCKFROST_KEY = process.env.BLOCKFROST_PROJECT_ID;
const FEE_COLLECTOR_ADDRESS = process.env.FEE_COLLECTOR_ADDRESS;
const WINTER_ADDRESS = process.env.WINTER_ADDRESS;
const RAW_ADA_FEE_AMOUNT = process.env.ADA_FEE_AMOUNT;
const RAW_PALM_FEE_AMOUNT = process.env.PALM_FEE_AMOUNT;
const PALM_UNIT = "b7c5cd554f3e83c8aa0900a0c9053284a5348244d23d0406c28eaf4d50414c4d0a";
const MIN_ADA_FEE_AMOUNT = `${process.env.MIN_ADA_FEE_AMOUNT}`;

interface SubmitTransactionRequest {
  changeAddress: string;
  utxos: any[];
  withPalm: boolean;
}

interface SubmitTransactionResponse {
  success: boolean;
  message: string;
  unsignedTx?: string;
  error?: string;
}

const convertAmountToLovelace = (amount: number): string => {
  return (amount * 1_000_000).toString();
};

const convertPalmToDecimals = (amount: number): string => {
  return (amount * 1_000_000).toString();
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SubmitTransactionResponse>
) {
  if (req.method !== 'POST') {
    return ApiErrorHandler.methodNotAllowed(res, ['POST']);
  }

  try {
    const { changeAddress, utxos, withPalm } = req.body as SubmitTransactionRequest;

    if (!changeAddress) {
      return ApiErrorHandler.badRequest(res, 'Wallet change address is required to build transaction');
    }

    if (!utxos || !Array.isArray(utxos) || utxos.length === 0) {
      return ApiErrorHandler.badRequest(res, 'Valid wallet UTXOs are required to build transaction');
    }

    if (typeof withPalm !== 'boolean') {
      return ApiErrorHandler.badRequest(res, 'Payment method must be specified (ADA or PALM)');
    }

    if (!BLOCKFROST_KEY) {
      return ApiErrorHandler.serviceUnavailable(res, 'Blockchain service is not properly configured');
    }

    if (!FEE_COLLECTOR_ADDRESS || !WINTER_ADDRESS) {
      return ApiErrorHandler.serviceUnavailable(res, 'Transaction addresses are not configured');
    }

    if (!RAW_ADA_FEE_AMOUNT || (!RAW_PALM_FEE_AMOUNT && withPalm)) {
      return ApiErrorHandler.serviceUnavailable(res, `${withPalm ? 'PALM' : 'ADA'} fee amount is not configured`);
    }

    const provider = new BlockfrostProvider(`${BLOCKFROST_KEY}`);
    const txBuilder = new MeshTxBuilder({
      fetcher: provider,
      verbose: true,
    });
    const adaAmount = convertAmountToLovelace(Number(RAW_ADA_FEE_AMOUNT));
    const palmAmount = withPalm ? convertPalmToDecimals(Number(RAW_PALM_FEE_AMOUNT)) : '0';
    const minAdaAmount = convertAmountToLovelace(Number(MIN_ADA_FEE_AMOUNT));

    let unsignedTx: string;

    if (withPalm) {
      unsignedTx = await txBuilder
        .txOut(`${WINTER_ADDRESS}`, [{ unit: "lovelace", quantity: minAdaAmount }])
        .txOut(`${FEE_COLLECTOR_ADDRESS}`, [{ unit: PALM_UNIT, quantity: palmAmount }])
        .changeAddress(changeAddress)
        .selectUtxosFrom(utxos)
        .complete();
    } else {
      unsignedTx = await txBuilder
        .txOut(`${FEE_COLLECTOR_ADDRESS}`, [{ unit: "lovelace", quantity: adaAmount }])
        .txOut(`${WINTER_ADDRESS}`, [{ unit: "lovelace", quantity: minAdaAmount }])
        .changeAddress(changeAddress)
        .selectUtxosFrom(utxos)
        .complete();
    }

    return res.status(200).json({
      success: true,
      message: `${withPalm ? 'PALM' : 'ADA'} transaction built successfully`,
      unsignedTx
    });

  } catch (error: any) {
    
    if (error.constructor.name === 'InputSelectionError' || error.message?.includes('UTxO Balance Insufficient')) {
      const { withPalm } = req.body as SubmitTransactionRequest;
      return ApiErrorHandler.badRequest(res, `Insufficient ${withPalm ? 'PALM tokens or ADA' : 'ADA'} in wallet to complete this transaction`);
    }

    if (error.message) {
      const { withPalm } = req.body as SubmitTransactionRequest;
      if (error.message.includes('insufficient') || error.message.includes('Balance Insufficient')) {
        return ApiErrorHandler.badRequest(res, `Insufficient ${withPalm ? 'PALM tokens or ADA' : 'ADA'} in wallet to complete payment`);
      }
      if (error.message.includes('utxo') || error.message.includes('UTXO')) {
        return ApiErrorHandler.badRequest(res, 'Invalid wallet UTXOs. Please refresh your wallet and try again.');
      }
      if (error.message.includes('address')) {
        return ApiErrorHandler.badRequest(res, 'Invalid wallet address provided');
      }
      if (error.message.includes('network') || error.message.includes('connection')) {
        return ApiErrorHandler.serviceUnavailable(res, USER_FRIENDLY_MESSAGES.NETWORK_CONNECTION_ERROR);
      }
      if (error.message.includes('InputSelection')) {
        return ApiErrorHandler.badRequest(res, 'Unable to select sufficient UTXOs from wallet. Please ensure you have enough funds.');
      }
    }

    if (error.failure === 'UTxO Balance Insufficient') {
      const { withPalm } = req.body as SubmitTransactionRequest;
      return ApiErrorHandler.badRequest(res, `Insufficient ${withPalm ? 'PALM tokens or ADA' : 'ADA'} in wallet to build transaction`);
    }

    if (error.response) {
      return ApiErrorHandler.externalApiError(res, 'Cardano Network', error);
    }

    
    const errorMessage = error.message || 'Transaction building failed';
    return ApiErrorHandler.badRequest(res, `Transaction failed: ${errorMessage}`);
  }
}