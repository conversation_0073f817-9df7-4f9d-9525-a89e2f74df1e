import "@/styles/globals.css";

import { Providers } from "@/lib/providers";
import { Layout } from "@/lib/components/layout/Layout";
import { SEO } from "@/lib/components/SEO";
import { SessionManager } from "@/lib/components/SessionManager";
import { seoConfig, defaultSEO } from "@/lib/config/seo";
import type { AppProps } from "next/app";
import { useRouter } from "next/router";

export default function App({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const pathname = router.pathname;
  
  const pageSEO = seoConfig[pathname as keyof typeof seoConfig] || {};

  return (
    <>
      <SEO
        title={pageSEO.title}
        description={pageSEO.description || defaultSEO.description}
        keywords={(pageSEO as any).keywords || defaultSEO.keywords}
      />
      <div className="relative min-h-screen">
        <div className="fixed inset-0 z-[-2] bg-background bg-[radial-gradient(ellipse_73%_73%_at_50%_-20%,rgba(60,60,60,1),rgba(255,255,255,0))]"></div>
        <Providers>
          <SessionManager />
          <Layout>
            <Component {...pageProps} />
          </Layout>
        </Providers>
      </div>
    </>
  );
}
