import { useState } from 'react'
import { DataSourceSelection } from '@/lib/components/create/DataSourceSelection'
import { TemplateSelection } from '@/lib/components/create/TemplateSelection'
import { FormBuilder } from '@/lib/components/formbuilder/FormBuilder'
import { DataConfirmation } from '@/lib/components/create/DataConfirmation'
import { CreateSubmission } from '@/lib/components/create/CreateSubmission'
import { CreateSuccess } from '@/lib/components/create/CreateSuccess'
import { TEMPLATES, YOUR_OWN_FORM } from '@/lib/templates'
import { useWallet } from '@meshsdk/react'
import { useNetworkStore } from '@/lib/store/network'
import { Alert, AlertDescription, AlertTitle } from '@/lib/components/ui/alert'
import { AlertTriangle } from 'lucide-react'

export type DataSource = 'own' | 'template' | null
export type FormData = Record<string, any>

export default function CreateRecords() {
  const [currentStep, setCurrentStep] = useState(1)
  const [dataSource, setDataSource] = useState<DataSource>(null)
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [selectedTemplateName, setSelectedTemplateName] = useState<string | null>(null)
  const [formData, setFormData] = useState<FormData>({})
  const [submissionResult, setSubmissionResult] = useState<any>(null)
  const [isPublic, setIsPublic] = useState<boolean>(true)
  const [paymentHash, setPaymentHash] = useState<string | null>(null)
  
  const { connected } = useWallet()
  const { selectedNetwork } = useNetworkStore()
  
  
  const handleDataSourceSelect = (source: DataSource) => {
    setDataSource(source)
    if (source === 'own') {
      setCurrentStep(3)
    } else {
      setCurrentStep(2)
    }
  }
  
  const handleTemplateSelect = (templateId: string, templateName: string) => {
    setSelectedTemplate(templateId)
    setSelectedTemplateName(templateName)
    setCurrentStep(3)
  }
  
  const handleFormSubmit = (data: FormData) => {
    setFormData(data)
    setCurrentStep(4)
  }
  
  const handleConfirm = (recordIsPublic?: boolean, paymentTxHash?: string) => {
    if (recordIsPublic !== undefined) {
      setIsPublic(recordIsPublic)
    }
    if (paymentTxHash) {
      setPaymentHash(paymentTxHash)
    }
    setCurrentStep(5)
  }
  
  const handleSubmissionSuccess = (result: any) => {
    setSubmissionResult(result)
    setCurrentStep(6)
  }
  
  const handleBack = () => {
    if (currentStep === 3 && dataSource === 'own') {
      setCurrentStep(1)
      setDataSource(null)
    } else {
      setCurrentStep(Math.max(1, currentStep - 1))
    }
  }
  
  const resetFlow = () => {
    setCurrentStep(1)
    setDataSource(null)
    setSelectedTemplate(null)
    setSelectedTemplateName(null)
    setFormData({})
  }
  
  const isMainnet = selectedNetwork === true
  const walletRequired = isMainnet && !connected

  return (
    <div className="min-h-screen py-6 md:py-12 px-4 md:px-6">
      <div className="max-w-6xl mx-auto">
        
        {walletRequired && (
<div className="flex justify-center pt-14">
          <Alert className="mb-8 border-destructive bg-destructive/10 w-full md:w-[50%] items-center">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Wallet Connection Required</AlertTitle>
            <AlertDescription>
              You must connect your wallet to create records on Mainnet. 
              Please connect your wallet first or switch to Preview network to continue without a wallet.
            </AlertDescription>
          </Alert>
        </div>
        )}
        
        {!walletRequired && currentStep === 1 && (
          <DataSourceSelection onSelect={handleDataSourceSelect} />
        )}

        {!walletRequired && currentStep === 2 && dataSource === 'template' && (
          <TemplateSelection 
            onSelect={handleTemplateSelect}
            onBack={handleBack}
          />
        )}

        {!walletRequired && currentStep === 3 && (
          <FormBuilder
            initialData={dataSource === 'own' ? YOUR_OWN_FORM.data : selectedTemplate ? (TEMPLATES as any)[selectedTemplate]?.data : undefined}
            templateId={selectedTemplate}
            templateName={dataSource === 'own' ? YOUR_OWN_FORM.name : selectedTemplateName}
            dataSource={dataSource}
            onSubmit={handleFormSubmit}
            onBack={handleBack}
          />
        )}
        
        {!walletRequired && currentStep === 4 && (
          <DataConfirmation
            formData={formData}
            onConfirm={handleConfirm}
            onBack={handleBack}
          />
        )}
        
        {!walletRequired && currentStep === 5 && (
          <CreateSubmission
            formData={formData}
            onSuccess={handleSubmissionSuccess}
            onBack={handleBack}
            isPublic={isPublic}
            paymentHash={paymentHash}
          />
        )}
        
        {!walletRequired && currentStep === 6 && (
          <CreateSuccess
            onCreateAnother={resetFlow}
            submissionResult={submissionResult}
          />
        )}
      </div>
    </div>
  )
}