import { useEffect, useState } from 'react'
import { useWallet } from '@meshsdk/react'
import { useWalletVerification } from '@/lib/store/wallet-verification'

export const useAutoConnect = () => {
  const { connect, connected, wallet } = useWallet()
  const { session, validateSession, clearVerification, setVerified } = useWalletVerification()
  const [autoConnecting, setAutoConnecting] = useState(false)
  const [autoConnectAttempted, setAutoConnectAttempted] = useState(false)

  useEffect(() => {
    const attemptAutoConnect = async () => {
      if (autoConnectAttempted || connected) {
        return
      }

      setAutoConnectAttempted(true)

      if (!session) {
        return
      }

      const now = Date.now()
      if (now > session.expiresAt) {
        return
      }

      setAutoConnecting(true)

      try {
        const walletId = session.walletId
        
        const walletList = (window as any).cardano
        if (!walletList || !walletList[walletId]) {
          return
        }

        await connect(walletId)
        
      } catch (error) {
        if (error instanceof Error && (error.message?.includes('wallet not found') || 
            error.message?.includes('not installed'))) {
          clearVerification()
        }
      } finally {
        setAutoConnecting(false)
      }
    }

    const timer = setTimeout(attemptAutoConnect, 1500)
    return () => clearTimeout(timer)
  }, [session, connected, autoConnectAttempted, connect, clearVerification])

  const [hasValidated, setHasValidated] = useState(false)
  
  useEffect(() => {
    if (!connected || !session) {
      setHasValidated(false)
      setAutoConnectAttempted(false)
    }
  }, [connected, session])
  
  useEffect(() => {
    setAutoConnectAttempted(false)
  }, [])

  useEffect(() => {
    const validateConnectionWithSession = async () => {
      if (!connected || !wallet || hasValidated) return
      setHasValidated(true)

      try {
        const walletAddress = await wallet.getChangeAddress()
        
        if (session && session.walletAddress === walletAddress) {
          const now = Date.now()
          if (now <= session.expiresAt) {
            setVerified(session.walletAddress, session.walletId)
          } else {
            clearVerification()
          }
        } else if (session && session.walletAddress !== walletAddress) {
          clearVerification()
        }
      } catch (error) {
      }
    }

    validateConnectionWithSession()
  }, [connected, wallet, hasValidated, session?.walletAddress, session?.walletId, session?.expiresAt, setVerified, clearVerification])

  const hasValidSession = session ? (Date.now() <= session.expiresAt) : false

  return {
    autoConnecting,
    hasValidSession
  }
}