import { useEffect } from 'react'
import { useRouter } from 'next/router'

interface UseSEOProps {
  title?: string
  description?: string
}

export const useSEO = ({ title, description }: UseSEOProps = {}) => {
  const router = useRouter()

  useEffect(() => {
    if (title) {
      document.title = `${title} | Trace.it`
    }

    if (description) {
      const metaDescription = document.querySelector('meta[name="description"]')
      if (metaDescription) {
        metaDescription.setAttribute('content', description)
      }
    }

    return () => {
      document.title = 'Trace.it - Blockchain Traceability on Cardano'
    }
  }, [title, description, router.asPath])
}