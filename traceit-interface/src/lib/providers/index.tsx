import { ThemeProvider } from "./theme-provider";
import { Toaster } from "@/lib/components/ui/sonner";
import { MeshProvider } from "@meshsdk/react";
import type { ReactNode } from "react";

interface AppProvidersProps {
  children: ReactNode;
}

export function Providers({ children }: AppProvidersProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="dark"
      enableSystem={false}
      disableTransitionOnChange
    >
      <MeshProvider>
      {children}
      <Toaster />
      </MeshProvider>
    </ThemeProvider>
  );
}