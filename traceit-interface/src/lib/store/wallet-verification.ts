import { create } from 'zustand'
import { persist } from 'zustand/middleware'

const SESSION_DURATION = 24 * 60 * 60 * 1000

const generateSessionToken = (walletAddress: string, timestamp: number): string => {
  const randomBytes = new Uint8Array(32)
  
  if (typeof window !== 'undefined' && window.crypto) {
    window.crypto.getRandomValues(randomBytes)
  } else {
    for (let i = 0; i < 32; i++) {
      randomBytes[i] = Math.floor(Math.random() * 256)
    }
  }
  
  const data = walletAddress + timestamp.toString() + Array.from(randomBytes).join('')
  
  const token = typeof window !== 'undefined' 
    ? btoa(data).replace(/[+/=]/g, '').substring(0, 32)
    : Buffer.from(data).toString('base64').replace(/[+/=]/g, '').substring(0, 32)
    
  return token
}

interface WalletSession {
  walletAddress: string
  walletId: string
  sessionToken: string
  expiresAt: number
  createdAt: number
}

interface WalletVerificationState {
  isVerified: boolean
  walletAddress: string
  walletId: string
  
  session: WalletSession | null
  
  setVerified: (walletAddress: string, walletId: string) => void
  clearVerification: () => void
  
  createSession: (walletAddress: string, walletId: string) => void
  validateSession: () => boolean
  clearExpiredSessions: () => void
  
  isWalletVerified: (address: string) => boolean
}

export const useWalletVerification = create<WalletVerificationState>()(
  persist(
    (set, get) => ({
      isVerified: false,
      walletAddress: '',
      walletId: '',
      session: null,
      
      setVerified: (walletAddress: string, walletId: string) => {
        const state = get()
        
        if (state.isVerified && 
            state.walletAddress === walletAddress && 
            state.session?.walletAddress === walletAddress &&
            state.session?.expiresAt > Date.now()) {
          return
        }
        
        const now = Date.now()
        const sessionToken = generateSessionToken(walletAddress, now)
        
        const session: WalletSession = {
          walletAddress,
          walletId,
          sessionToken,
          expiresAt: now + SESSION_DURATION,
          createdAt: now
        }
        
        set({
          isVerified: true,
          walletAddress,
          walletId,
          session
        })
        
      },
      
      clearVerification: () => {
        set({
          isVerified: false,
          walletAddress: '',
          walletId: '',
          session: null
        })
        
        try {
          if (typeof window !== 'undefined') {
            localStorage.removeItem('wallet-verification-storage')
          }
        } catch (error) {
        }
      },
      
      createSession: (walletAddress: string, walletId: string) => {
        const now = Date.now()
        const sessionToken = generateSessionToken(walletAddress, now)
        
        const session: WalletSession = {
          walletAddress,
          walletId,
          sessionToken,
          expiresAt: now + SESSION_DURATION,
          createdAt: now
        }
        
        set({ session })
      },
      
      validateSession: (): boolean => {
        const state = get()
        const now = Date.now()
        
        if (!state.session) {
          return false
        }
        
        if (now > state.session.expiresAt) {
          return false
        }
        
        return true
      },
      
      clearExpiredSessions: () => {
        const state = get()
        const now = Date.now()
        
        if (state.session && now > state.session.expiresAt) {
          set({
            isVerified: false,
            walletAddress: '',
            walletId: '',
            session: null
          })
        }
      },
      
      isWalletVerified: (address: string) => {
        const state = get()
        const { validateSession } = get()
        
        if (!state.isVerified || state.walletAddress !== address) {
          return false
        }
        
        return validateSession()
      }
    }),
    {
      name: 'wallet-verification-storage',
      partialize: (state) => ({
        isVerified: state.isVerified,
        walletAddress: state.walletAddress,
        walletId: state.walletId,
        session: state.session
      })
    }
  )
)