import { create } from 'zustand';

interface NetworkStore {
  selectedNetwork: boolean;
  refreshKey: number;
  hasHydrated: boolean;
  
  setSelectedNetwork: (isMainnet: boolean) => void;
  toggleNetwork: () => void;
  triggerRefresh: () => void;
  setHasHydrated: (state: boolean) => void;
}

const storeNetwork = (isMainnet: boolean): void => {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem('selectedNetwork', String(isMainnet));
  } catch {
  }
};

export const useNetworkStore = create<NetworkStore>((set, get) => ({
  selectedNetwork: false,
  refreshKey: 0,
  hasHydrated: false,
  
  setSelectedNetwork: (isMainnet: boolean) => {
    storeNetwork(isMainnet);
    set((state) => ({
      selectedNetwork: isMainnet,
      refreshKey: state.refreshKey + 1, 
    }));
  },
  
  toggleNetwork: () => {
    const { selectedNetwork } = get();
    const newNetwork = !selectedNetwork;
    storeNetwork(newNetwork);
    set((state) => ({
      selectedNetwork: newNetwork,
      refreshKey: state.refreshKey + 1, 
    }));
  },
  
  triggerRefresh: () => {
    set((state) => ({
      refreshKey: state.refreshKey + 1,
    }));
  },
  
  setHasHydrated: (state: boolean) => {
    set({ hasHydrated: state });
  },
}));

if (typeof window !== 'undefined') {
  const hydrateStore = () => {
    try {
      const stored = localStorage.getItem('selectedNetwork');
      if (stored !== null) {
        useNetworkStore.setState({ 
          selectedNetwork: stored === 'true',
          hasHydrated: true 
        });
      } else {
        useNetworkStore.setState({ hasHydrated: true });
      }
    } catch {
      useNetworkStore.setState({ hasHydrated: true });
    }
  };
  
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', hydrateStore);
  } else {
    hydrateStore();
  }
}