import { pgTable, varchar, text, jsonb, timestamp, uuid, index, boolean } from 'drizzle-orm/pg-core';
import { v4 as uuidv4 } from 'uuid';

const generateShortId = (): string => {
  const chars = '0123456789abcdefghijklmnopqrstuvwxyz';
  const part1 = Array.from({ length: 4 }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
  const part2 = Array.from({ length: 4 }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
  return `${part1}-${part2}`;
};

export const wallets = pgTable('wallets', {
  id: uuid('id').primaryKey().$defaultFn(() => uuidv4()),
  walletAddress: varchar('wallet_address', { length: 255 }).notNull().unique(),
  walletName: varchar('wallet_name', { length: 100 }),
  nonce: varchar('nonce', { length: 255 }),
  walletSignature: text('wallet_signature'),
  lastActive: timestamp('last_active', { withTimezone: true }).notNull().defaultNow(),
  role: varchar('role', { length: 20 }).notNull().default('user'),
  isVerified: boolean('is_verified').notNull().default(false),
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
}, (table) => [
  index('idx_wallets_address').on(table.walletAddress),
  index('idx_wallets_nonce').on(table.nonce),
  index('idx_wallets_verified').on(table.isVerified),
]);

export const records = pgTable('records', {
  id: varchar('id', { length: 9 }).primaryKey().$defaultFn(() => generateShortId()),
  walletId: uuid('wallet_id').notNull().references(() => wallets.id),
  txHash: varchar('tx_hash', { length: 255 }).notNull().default(''),
  paymentHash: varchar('payment_hash', { length: 255 }).notNull(),
  ipfs: varchar('ipfs', { length: 255 }).unique(),
  json: jsonb('json').notNull(),
  isPublic: boolean('is_public').notNull().default(false),
  networkId: varchar('network_id', { length: 50 }).notNull().default('preview'),
  winterId: varchar('winter_id', { length: 255 }).unique(),
  winterPublished: boolean('winter_published').notNull().default(false),
  winterStatus: varchar('winter_status', { length: 50 }),
  createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
}, (table) => [
  index('idx_records_wallet_id').on(table.walletId),
  index('idx_records_tx_hash').on(table.txHash),
  index('idx_records_network').on(table.networkId),
  index('idx_records_is_public').on(table.isPublic),
  index('idx_records_created_at').on(table.createdAt),
  index('idx_records_json').using('gin', table.json),
  index('idx_records_winter_id').on(table.winterId),
]);

export type Wallet = typeof wallets.$inferSelect;
export type NewWallet = typeof wallets.$inferInsert;
export type Record = typeof records.$inferSelect;
export type NewRecord = typeof records.$inferInsert;