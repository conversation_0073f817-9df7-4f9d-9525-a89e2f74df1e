import type { NextApiResponse } from 'next';

export interface ApiError {
  success: false;
  message: string;
  error?: string;
  code?: string;
  details?: any;
}

export interface ApiSuccess<T = any> {
  success: true;
  message?: string;
  data?: T;
  [key: string]: any;
}

export type ApiResponse<T = any> = ApiSuccess<T> | ApiError;

export class ApiErrorHandler {
  static badRequest(res: NextApiResponse, message: string, details?: any): void {
    res.status(400).json({
      success: false,
      message,
      error: 'Bad Request',
      code: 'BAD_REQUEST',
      details
    });
  }

  static unauthorized(res: NextApiResponse, message: string = 'Authentication required'): void {
    res.status(401).json({
      success: false,
      message,
      error: 'Unauthorized',
      code: 'UNAUTHORIZED'
    });
  }

  static forbidden(res: NextApiResponse, message: string = 'Access denied'): void {
    res.status(403).json({
      success: false,
      message,
      error: 'Forbidden',
      code: 'FORBIDDEN'
    });
  }

  static notFound(res: NextApiResponse, message: string = 'Resource not found'): void {
    res.status(404).json({
      success: false,
      message,
      error: 'Not Found',
      code: 'NOT_FOUND'
    });
  }

  static methodNotAllowed(res: NextApiResponse, allowedMethods: string[]): void {
    res.setHeader('Allow', allowedMethods);
    res.status(405).json({
      success: false,
      message: `Method not allowed. Allowed methods: ${allowedMethods.join(', ')}`,
      error: 'Method Not Allowed',
      code: 'METHOD_NOT_ALLOWED'
    });
  }

  static conflict(res: NextApiResponse, message: string): void {
    res.status(409).json({
      success: false,
      message,
      error: 'Conflict',
      code: 'CONFLICT'
    });
  }

  static validationError(res: NextApiResponse, message: string, details?: any): void {
    res.status(422).json({
      success: false,
      message,
      error: 'Validation Error',
      code: 'VALIDATION_ERROR',
      details
    });
  }

  static tooManyRequests(res: NextApiResponse, message: string = 'Too many requests'): void {
    res.status(429).json({
      success: false,
      message,
      error: 'Too Many Requests',
      code: 'RATE_LIMIT_EXCEEDED'
    });
  }

  static internalError(res: NextApiResponse, message: string = 'An unexpected error occurred'): void {
    res.status(500).json({
      success: false,
      message,
      error: 'Internal Server Error',
      code: 'INTERNAL_ERROR'
    });
  }

  static serviceUnavailable(res: NextApiResponse, message: string = 'Service temporarily unavailable'): void {
    res.status(503).json({
      success: false,
      message,
      error: 'Service Unavailable',
      code: 'SERVICE_UNAVAILABLE'
    });
  }

  static databaseError(res: NextApiResponse, error: any): void {
    if (error.code === '23505') {
      return this.conflict(res, 'This record already exists');
    }

    if (error.code === '23503') {
      return this.badRequest(res, 'Invalid reference data provided');
    }

    if (error.code === '23502') {
      return this.badRequest(res, 'Required field is missing');
    }

    this.internalError(res, 'Database operation failed');
  }

  static externalApiError(res: NextApiResponse, service: string, error: any): void {

    let message = `${service} service is currently unavailable`;
    
    if (error.response) {
      const status = error.response.status;
      if (status === 429) {
        message = `${service} rate limit exceeded. Please try again later.`;
      } else if (status >= 500) {
        message = `${service} is experiencing issues. Please try again later.`;
      } else if (status === 400) {
        message = `Invalid request to ${service}`;
      } else {
        message = `${service} returned an error`;
      }
    } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      message = `Cannot connect to ${service}. Please try again later.`;
    }

    this.serviceUnavailable(res, message);
  }

  static blockchainError(res: NextApiResponse, error: any): void {

    let message = 'Blockchain operation failed';

    if (error.message) {
      if (error.message.includes('insufficient')) {
        message = 'Insufficient funds in wallet';
      } else if (error.message.includes('utxo') || error.message.includes('UTXO')) {
        message = 'Invalid wallet state. Please refresh and try again.';
      } else if (error.message.includes('network')) {
        message = 'Network connectivity issue. Please check your connection.';
      } else if (error.message.includes('signature')) {
        message = 'Transaction signature verification failed';
      }
    }

    this.badRequest(res, message);
  }

  static success<T>(res: NextApiResponse, data: T, message?: string): void {
    res.status(200).json({
      success: true,
      message,
      ...data
    });
  }
}

export const USER_FRIENDLY_MESSAGES = {
  WALLET_NOT_CONNECTED: 'Please connect your wallet to continue',
  WALLET_NOT_VERIFIED: 'Please verify your wallet ownership first',
  WALLET_SIGNATURE_INVALID: 'Wallet signature verification failed',
  WALLET_ADDRESS_REQUIRED: 'Wallet address is required',
  WALLET_INSUFFICIENT_FUNDS: 'Insufficient funds in your wallet',
  RECORD_NOT_FOUND: 'This record does not exist or has been removed',
  RECORD_ACCESS_DENIED: 'You don\'t have permission to view this private record',
  RECORD_CREATION_FAILED: 'Failed to create record. Please try again.',
  RECORD_UPDATE_FAILED: 'Failed to update record. Please try again.',
  NETWORK_CONNECTION_ERROR: 'Network connection issue. Please check your internet connection.',
  SERVICE_UNAVAILABLE: 'Service is temporarily unavailable. Please try again in a few minutes.',
  PAYMENT_REQUIRED: 'Payment is required to create records on mainnet',
  PAYMENT_FAILED: 'Payment transaction failed. Please try again.',
  PAYMENT_INSUFFICIENT: 'Insufficient funds for payment',
  WINTER_API_ERROR: 'Winter Protocol service is currently unavailable',
  WINTER_VALIDATION_FAILED: 'Winter Protocol validation failed',
  IPFS_UPLOAD_FAILED: 'Failed to upload data to IPFS',
  INVALID_REQUEST: 'Invalid request. Please check your input and try again.',
  INTERNAL_ERROR: 'An unexpected error occurred. Please try again.',
  RATE_LIMIT: 'Too many requests. Please wait a moment before trying again.',
};