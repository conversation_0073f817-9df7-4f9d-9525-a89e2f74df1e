export interface WalletErrorInfo {
  message: string;
  isUserCancelled: boolean;
  shouldRetry: boolean;
}

export const extractWalletErrorMessage = (error: any): string | null => {
  if (typeof error === 'string' && error.trim()) {
    return error.trim()
  }

  const possibleMessages = [
    error?.info,
    error?.message,
    error?.error,
    error?.details,
    error?.description,
    error?.reason,
    error?.data,
    error?.errorMessage,
    error?.statusText,
    error?.body?.message,
    error?.response?.data?.message,
    error?.response?.data?.error,
    error?.response?.statusText,
  ]
  
  if (error && typeof error === 'object') {
    const errorString = error.toString()
    if (errorString && errorString !== '[object Object]' && errorString !== 'Error') {
      possibleMessages.unshift(errorString)
    }
    
    if (error.error && typeof error.error === 'object') {
      possibleMessages.push(error.error.message, error.error.info, error.error.description)
    }
    
    if (error.response && typeof error.response === 'object') {
      possibleMessages.push(error.response.message, error.response.error, error.response.statusText)
      if (error.response.data && typeof error.response.data === 'object') {
        possibleMessages.push(error.response.data.message, error.response.data.error, error.response.data.info)
      }
    }
  }
  
  for (const msg of possibleMessages) {
    if (typeof msg === 'string' && msg.trim() && msg !== 'undefined' && msg !== 'null') {
      return msg.trim()
    }
  }
  
  return null
}

export const isUserCancelledError = (error: any, errorMessage: string | null): boolean => {
  if (error?.code === 2 && error?.name === 'TxSignError') {
    return true
  }
  
  if (error?.code === 3 && error?.name === 'DataSignError') {
    return true
  }
  
  if (errorMessage) {
    const lowerMessage = errorMessage.toLowerCase()
    return lowerMessage.includes('user declined') || 
           lowerMessage.includes('cancelled') || 
           lowerMessage.includes('rejected') ||
           lowerMessage.includes('user canceled') ||
           lowerMessage.includes('denied by user') ||
           lowerMessage.includes('user rejected') ||
           lowerMessage.includes('declined') ||
           lowerMessage.includes('aborted')
  }
  
  return false
}

export const processWalletError = (error: any, context: string = 'operation'): WalletErrorInfo => {
  const rawMessage = extractWalletErrorMessage(error)
  const isUserCancelled = isUserCancelledError(error, rawMessage)
  
  let userMessage: string
  let shouldRetry = true
  
  if (isUserCancelled) {
    userMessage = `${context} was cancelled by user`
    shouldRetry = false
  } else if (rawMessage) {
    const lowerMessage = rawMessage.toLowerCase()
    
    if (lowerMessage.includes('insufficient') || 
        lowerMessage.includes('not enough') ||
        lowerMessage.includes('balance')) {
      userMessage = 'Insufficient funds to complete transaction'
      shouldRetry = false
    } else if (lowerMessage.includes('utxo')) {
      userMessage = 'Invalid wallet state. Please refresh your wallet and try again.'
      shouldRetry = true
    } else if (lowerMessage.includes('network') || 
               lowerMessage.includes('connection') ||
               lowerMessage.includes('timeout')) {
      userMessage = 'Network connection failed. Please check your internet and try again.'
      shouldRetry = true
    } else if (lowerMessage.includes('invalid transaction') ||
               lowerMessage.includes('malformed')) {
      userMessage = 'Transaction format error. Please try again.'
      shouldRetry = true
    } else if (lowerMessage.includes('locked') ||
               lowerMessage.includes('busy')) {
      userMessage = 'Wallet is busy. Please wait and try again.'
      shouldRetry = true
    } else if (lowerMessage.includes('fee') ||
               lowerMessage.includes('gas')) {
      userMessage = 'Transaction fee error. Please try again.'
      shouldRetry = true
    } else {
      if (rawMessage.length < 100 && !rawMessage.includes('stack') && 
          !rawMessage.includes('undefined') && !rawMessage.includes('null')) {
        userMessage = rawMessage
      } else {
        userMessage = `${context} failed. Please try again.`
      }
      shouldRetry = true
    }
  } else {
    if (error?.name === 'TxSignError') {
      if (error.code === 2) {
        userMessage = 'Transaction signing was declined by user'
        shouldRetry = false
      } else {
        userMessage = 'Transaction signing failed'
        shouldRetry = true
      }
    } else if (error?.name === 'TxSendError') {
      userMessage = 'Failed to submit transaction to the network'
      shouldRetry = true
    } else if (error?.name === 'DataSignError') {
      if (error.code === 3) {
        userMessage = 'Data signing was declined by user'
        shouldRetry = false
      } else {
        userMessage = 'Data signing failed'
        shouldRetry = true
      }
    } else if (error?.name === 'APIError') {
      userMessage = 'Wallet API error occurred'
      shouldRetry = true
    } else {
      userMessage = `${context} failed. Please try again.`
      shouldRetry = true
    }
  }

  return {
    message: userMessage,
    isUserCancelled,
    shouldRetry
  }
}

export const WALLET_ERROR_CODES = {
  USER_DECLINED_TX: 2,
  USER_DECLINED_DATA: 3,
  INSUFFICIENT_FUNDS: 4,
  NETWORK_ERROR: 5,
  INVALID_TX: 6,
} as const

export const WALLET_ERROR_NAMES = {
  TX_SIGN_ERROR: 'TxSignError',
  TX_SEND_ERROR: 'TxSendError', 
  DATA_SIGN_ERROR: 'DataSignError',
  API_ERROR: 'APIError',
  NETWORK_ERROR: 'NetworkError',
  VALIDATION_ERROR: 'ValidationError',
} as const