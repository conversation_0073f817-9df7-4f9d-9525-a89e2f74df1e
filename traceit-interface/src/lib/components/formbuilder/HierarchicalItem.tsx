import React, { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/lib/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/lib/components/ui/popover'
import { Plus, Type, Hash, Calendar, Link, Mail, AlignLeft, ChevronDown, ChevronRight, Folder, Clock, MapPin, GripVertical } from 'lucide-react'
import { FormItem, FieldType, FormField, FormCategory } from './types'
import { toast } from 'sonner'
import { getPlaceholderForType, sanitizeFieldName } from './utils'

interface HierarchicalItemProps {
  item: FormItem
  onUpdate: (id: string, updates: Partial<FormField | FormCategory>) => void
  onDelete: (id: string) => void
  onAddField: (parentId: string, fieldType: FieldType | 'category') => void
  isLast?: boolean
  lineNumber: number
  allItems?: FormItem[]
}

const FIELD_TYPE_OPTIONS = [
  { id: 'category', label: 'Category', icon: <Folder className="w-4 h-4" /> },
  { id: 'string', label: 'Text', icon: <Type className="w-4 h-4" /> },
  { id: 'textarea', label: 'Long Text', icon: <AlignLeft className="w-4 h-4" /> },
  { id: 'number', label: 'Number', icon: <Hash className="w-4 h-4" /> },
  { id: 'date', label: 'Date', icon: <Calendar className="w-4 h-4" /> },
  { id: 'timestamp', label: 'Timestamp', icon: <Clock className="w-4 h-4" /> },
  { id: 'geolocation', label: 'Geolocation', icon: <MapPin className="w-4 h-4" /> },
  { id: 'url', label: 'URL', icon: <Link className="w-4 h-4" /> },
  { id: 'email', label: 'Email', icon: <Mail className="w-4 h-4" /> },
]


interface JsonEditableProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  type?: 'key' | 'value'
  dataType?: FieldType
  isCategory?: boolean
  onGeolocationRequest?: () => void
}

const JsonEditable: React.FC<JsonEditableProps> = ({ 
  value, 
  onChange, 
  placeholder = 'Click to edit',
  className = '',
  type = 'value',
  dataType = 'string',
  isCategory = false,
  onGeolocationRequest
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(value)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    setEditValue(value)
  }, [value])

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  const handleSave = () => {
    onChange(editValue)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditValue(value)
    setIsEditing(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  const getValueColor = () => {
    if (type === 'key' && isCategory) return 'text-cyan-400' // Categories in cyan
    if (type === 'key') return 'text-blue-400' // Regular fields in blue
    if (dataType === 'number') return 'text-orange-400'
    if (dataType === 'date' || dataType === 'timestamp') return 'text-purple-400'
    if (dataType === 'geolocation') return 'text-pink-400'
    if (dataType === 'url' || dataType === 'email') return 'text-cyan-400'
    return 'text-green-400' // string default
  }

  if (isEditing) {
    return (
      <input
        ref={inputRef}
        type={dataType === 'date' ? 'date' : 'text'}
        value={editValue}
        placeholder={placeholder}
        onChange={(e) => setEditValue(e.target.value)}
        onBlur={handleSave}
        onKeyDown={handleKeyDown}
        className={`bg-transparent outline-none font-mono border-b border-border ${getValueColor()} ${className} placeholder:text-muted-foreground/30 placeholder:italic`}
        style={{ minWidth: '4ch', width: `${Math.max(editValue.length + 2, Math.max(placeholder?.length || 0, 6))}ch` }}
      />
    )
  }

  const displayContent = value ? 
    (type === 'value' && dataType !== 'number' ? `"${value}"` : value) : 
    (type === 'value' && dataType !== 'number' ? `"${placeholder}"` : placeholder)

  const handleClick = () => {
    if (type === 'value' && dataType === 'geolocation' && !value && onGeolocationRequest) {
      onGeolocationRequest()
    } else {
      setIsEditing(true)
    }
  }

  return (
    <span
      onClick={handleClick}
      className={`cursor-text hover:bg-muted/50 hover:outline hover:outline-1 hover:outline-border px-0.5 py-0.5 rounded transition-all font-mono inline-block ${
        !value ? 'text-muted-foreground/40 italic' : getValueColor()
      } ${className} ${type === 'value' && dataType === 'geolocation' && !value ? 'bg-blue-50 hover:bg-blue-100 border border-blue-200' : ''}`}
      title={type === 'value' && dataType === 'geolocation' && !value ? 'Click to get current location' : 'Click to edit'}
      style={{ minWidth: !value ? '4ch' : 'auto' }}
    >
      {displayContent}
      {type === 'value' && dataType === 'geolocation' && !value && (
        <span className="ml-1 text-blue-500">📍</span>
      )}
    </span>
  )
}

export const HierarchicalItem: React.FC<HierarchicalItemProps> = ({
  item,
  onUpdate,
  onDelete,
  onAddField,
  isLast = true,
  lineNumber,
  allItems = []
}) => {
  const [addOpen, setAddOpen] = useState(false)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const isField = 'type' in item
  const hasChildren = !isField && 'children' in item && item.children.length > 0
  
  const checkForDuplicateName = (name: string, currentId: string, items: FormItem[], level: number): boolean => {
    return items.some(sibling => 
      sibling.id !== currentId && 
      sibling.name === name && 
      sibling.level === level
    )
  }
  
  const isDuplicateName = checkForDuplicateName(item.name, item.id, allItems, item.level)
  
  const indentLevel = item.level + 1
  const indentWidth = indentLevel * 1.5
  
  const handleNameChange = (newName: string) => {
    const sanitizedName = sanitizeFieldName(newName.trim())
    const isDuplicate = checkForDuplicateName(sanitizedName, item.id, allItems, item.level)
    
    if (isDuplicate) {
      toast.error(`Field name "${sanitizedName}" already exists at this level. JSON field names must be unique.`)
      return
    }
    
    onUpdate(item.id, { name: sanitizedName })
  }

  const handleValueChange = (newValue: string) => {
    if (isField) {
      onUpdate(item.id, { value: newValue })
    }
  }

  const handleGeolocationRequest = async () => {
    try {
      if (navigator.geolocation) {
        toast.info('Requesting location permission...')
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
          })
        })
        const location = `${position.coords.latitude}, ${position.coords.longitude}`
        onUpdate(item.id, { value: location })
        toast.success(`Location updated: ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}`)
      } else {
        toast.error('Geolocation is not supported by this browser')
      }
    } catch {
      toast.error('Location access denied or unavailable')
    }
  }

  const handleAddFieldType = async (fieldType: FieldType | 'category') => {
    if (fieldType === 'geolocation') {
      try {
        if (navigator.geolocation) {
          toast.info('Requesting location permission...')
          const position = await new Promise<GeolocationPosition>((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 60000
            })
          })
          toast.success(`Location acquired: ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}`)
        } else {
          toast.error('Geolocation is not supported by this browser')
        }
      } catch {
        toast.error('Location access denied or unavailable')
      }
    }
    
    if (fieldType === 'timestamp') {
      const now = new Date()
      toast.success(`Timestamp: ${now.toLocaleString()}`)
    }
    
    onAddField(item.id, fieldType)
    setAddOpen(false)
  }

  if (isField) {
    return (
      <div className="group relative flex items-center transition-colors py-0.5 leading-6 hover:bg-muted/20">
        <div className="w-4 flex items-center justify-center">
          <div className="flex items-center justify-center text-muted-foreground/20 p-1 -m-1 rounded">
            <GripVertical className="w-3 h-3" />
          </div>
        </div>
        
        <div className="w-8 md:w-10 text-xs text-muted-foreground/40 text-right pr-2 md:pr-3 font-mono select-none">
          {lineNumber}
        </div>
        
        <div className="flex items-center font-mono text-xs md:text-sm flex-1">
          <div className="select-none" style={{ width: `${Math.max(indentWidth * 0.75, 0.5)}rem` }}></div>
          
          <span className="text-muted-foreground/60 select-none">&quot;</span>
          
          <JsonEditable
            value={item.name}
            onChange={handleNameChange}
            placeholder="field"
            type="key"
            className={`min-w-0 ${isDuplicateName ? 'bg-red-500/20 border border-red-500/50' : ''}`}
            isCategory={false}
          />
          
          <span className="text-muted-foreground/60 select-none">&quot;: </span>
          
          <JsonEditable
            value={item.value}
            onChange={handleValueChange}
            placeholder={item.placeholder || getPlaceholderForType(item.type)}
            type="value"
            dataType={item.type}
            className="min-w-0"
            onGeolocationRequest={item.type === 'geolocation' ? handleGeolocationRequest : undefined}
          />
          
          {!isLast && <span className="text-muted-foreground/60 select-none">,</span>}
        </div>
        
        <div className="flex items-center gap-1 md:opacity-0 md:group-hover:opacity-100 opacity-100 transition-opacity ml-auto">
          <Popover open={addOpen} onOpenChange={setAddOpen}>
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 md:h-5 md:w-5 p-0 text-muted-foreground/40 hover:text-blue-400 touch-manipulation"
              >
                <Plus className="w-3 h-3" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-40 p-1" align="end">
              <div className="space-y-px">
                {FIELD_TYPE_OPTIONS.map((option) => (
                  <button
                    key={option.id}
                    onClick={() => handleAddFieldType(option.id as FieldType | 'category')}
                    className="w-full flex items-center gap-2 px-2 py-1.5 md:py-1 text-xs hover:bg-muted/70 active:bg-muted transition-colors text-left rounded-sm touch-manipulation"
                  >
                    <div className="text-muted-foreground">
                      {option.icon}
                    </div>
                    {option.label}
                  </button>
                ))}
              </div>
            </PopoverContent>
          </Popover>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => onDelete(item.id)}
            className="h-6 w-6 md:h-5 md:w-5 p-0 text-muted-foreground/40 hover:text-red-400 touch-manipulation"
          >
            ×
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="group relative flex items-center transition-colors py-0.5 leading-6 hover:bg-muted/20">
        <div className="w-4 flex items-center justify-center">
          <div className="flex items-center justify-center text-muted-foreground/20 p-1 -m-1 rounded">
            <GripVertical className="w-3 h-3" />
          </div>
        </div>
        
        <div className="w-8 md:w-10 text-xs text-muted-foreground/40 text-right pr-2 md:pr-3 font-mono select-none">
          {lineNumber}
        </div>
        
        <div className="flex items-center font-mono text-xs md:text-sm flex-1">
          <div className="select-none" style={{ width: `${Math.max(indentWidth * 0.75, 0.5)}rem` }}></div>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-4 w-4 p-0 text-muted-foreground/40 hover:text-foreground mr-1"
          >
            {isCollapsed ? <ChevronRight className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />}
          </Button>
          
          <span className="text-muted-foreground/60 select-none">&quot;</span>
          
          <JsonEditable
            value={item.name}
            onChange={handleNameChange}
            placeholder="category"
            type="key"
            className={`font-medium ${isDuplicateName ? 'bg-red-500/20 border border-red-500/50' : ''}`}
            isCategory={true}
          />
          
          <span className="text-muted-foreground/60 select-none">&quot;: {'{'}</span>
          
          {isCollapsed && hasChildren && (
            <span className="text-muted-foreground/40 ml-2 text-xs">
              {item.children.length} items...
            </span>
          )}
          
          {isCollapsed && (
            <span className="text-muted-foreground/60 select-none">{'}'}</span>
          )}
          
          {!isLast && <span className="text-muted-foreground/60 select-none">,</span>}
        </div>
        
        <div className="flex items-center gap-1 md:opacity-0 md:group-hover:opacity-100 opacity-100 transition-opacity ml-auto">
          <Popover open={addOpen} onOpenChange={setAddOpen}>
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 w-6 md:h-5 md:w-5 p-0 text-muted-foreground/40 hover:text-blue-400 touch-manipulation"
              >
                <Plus className="w-3 h-3" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-40 p-1" align="end">
              <div className="space-y-px">
                {FIELD_TYPE_OPTIONS.map((option) => (
                  <button
                    key={option.id}
                    onClick={() => handleAddFieldType(option.id as FieldType | 'category')}
                    className="w-full flex items-center gap-2 px-2 py-1.5 md:py-1 text-xs hover:bg-muted/70 active:bg-muted transition-colors text-left rounded-sm touch-manipulation"
                  >
                    <div className="text-muted-foreground">
                      {option.icon}
                    </div>
                    {option.label}
                  </button>
                ))}
              </div>
            </PopoverContent>
          </Popover>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={() => onDelete(item.id)}
            className="h-6 w-6 md:h-5 md:w-5 p-0 text-muted-foreground/40 hover:text-red-400 touch-manipulation"
          >
            ×
          </Button>
        </div>
      </div>

      {hasChildren && !isCollapsed && (
        <div>
            {item.children.map((child, index) => {
            let childLineNumber = lineNumber + 1
            for (let i = 0; i < index; i++) {
              const prevChild = item.children[i]
              childLineNumber += 1
              if ('children' in prevChild && prevChild.children.length > 0) {
                childLineNumber += prevChild.children.length + 1
              }
            }
            
            return (
              <HierarchicalItem
                key={child.id}
                item={child}
                onUpdate={onUpdate}
                onDelete={onDelete}
                onAddField={onAddField}
                isLast={index === item.children.length - 1}
                lineNumber={childLineNumber}
                allItems={item.children}
              />
            )
          })}
          
          <div className="flex items-center py-0.5 hover:bg-muted/20 transition-colors">
            <div className="w-4 flex items-center justify-center text-muted-foreground/20">
              <GripVertical className="w-3 h-3" />
            </div>
            
            <div className="w-8 md:w-10 text-xs text-muted-foreground/40 text-right pr-2 md:pr-3 font-mono select-none">
              {(() => {
                let closingLine = lineNumber + 1
                item.children.forEach(child => {
                  closingLine += 1
                  if ('children' in child && child.children.length > 0) {
                    closingLine += child.children.length + 1
                  }
                })
                return closingLine
              })()}
            </div>
            <div className="font-mono text-xs md:text-sm flex items-center">
              <div className="select-none" style={{ width: `${Math.max(indentWidth * 0.75, 0.5)}rem` }}></div>
              <span className="text-muted-foreground/60 select-none">{'}'}</span>
              {!isLast && <span className="text-muted-foreground/60 select-none">,</span>}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}