export type FieldType = 'string' | 'number' | 'date' | 'url' | 'email' | 'textarea' | 'array' | 'timestamp' | 'geolocation'

export interface FormField {
  id: string
  name: string
  type: FieldType
  value: string
  level: number // For indentation
  placeholder?: string
  required?: boolean
}

export interface FormCategory {
  id: string
  name: string
  level: number // For indentation
  children: (FormField | FormCategory)[]
  isCollapsed?: boolean
}

export type FormItem = FormField | FormCategory

export interface FormSchema {
  items: FormItem[]
}

export interface JsonData {
  [key: string]: any
}