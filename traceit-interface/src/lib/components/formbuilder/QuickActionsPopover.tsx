import React from 'react'
import { Plus, Folder, Type, Hash, Calendar, Link, Mail, AlignLeft, Clock, MapPin } from 'lucide-react'
import { Button } from '@/lib/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/lib/components/ui/dropdown-menu'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/lib/components/ui/drawer'

interface QuickActionsPopoverProps {
  onAdd: (type: string) => void
  fieldCount?: number
  fieldLimit?: number
}

export const QuickActionsPopover: React.FC<QuickActionsPopoverProps> = ({ onAdd, fieldCount, fieldLimit }) => {
  const actions = [
    {
      icon: <Folder className="w-4 h-4" />,
      label: "Add Category",
      type: 'category'
    },
    {
      icon: <Type className="w-4 h-4" />,
      label: "Add Text Field",
      type: 'string'
    },
    {
      icon: <AlignLeft className="w-4 h-4" />,
      label: "Add Long Text",
      type: 'textarea'
    },
    {
      icon: <Hash className="w-4 h-4" />,
      label: "Add Number",
      type: 'number'
    },
    {
      icon: <Calendar className="w-4 h-4" />,
      label: "Add Date",
      type: 'date'
    },
    {
      icon: <Clock className="w-4 h-4" />,
      label: "Add Timestamp",
      type: 'timestamp'
    },
    {
      icon: <MapPin className="w-4 h-4" />,
      label: "Add Geolocation",
      type: 'geolocation'
    },
    {
      icon: <Link className="w-4 h-4" />,
      label: "Add URL",
      type: 'url'
    },
    {
      icon: <Mail className="w-4 h-4" />,
      label: "Add Email",
      type: 'email'
    },
  ]


  return (
    <>
      <div className="hidden md:block">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-9 gap-1 px-3"
            >
              <Plus className="w-3 h-3" />
              <span className="text-sm">Add</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end">
            <DropdownMenuLabel>Add to Form</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              {actions.slice(0, 2).map((action, index) => (
                <DropdownMenuItem key={index} onClick={() => onAdd(action.type)}>
                  {action.icon}
                  <span>{action.label}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              {actions.slice(2).map((action, index) => (
                <DropdownMenuItem key={index + 2} onClick={() => onAdd(action.type)}>
                  {action.icon}
                  <span>{action.label}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="block md:hidden">
        <Drawer>
          <DrawerTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-9 gap-1 px-3"
            >
              <Plus className="w-3 h-3" />
              <span className="text-sm">Add</span>
            </Button>
          </DrawerTrigger>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>Add to Form</DrawerTitle>
            </DrawerHeader>
            <div className="px-4 pb-4 space-y-2">
              {actions.map((action, index) => (
                <DrawerClose key={index} asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-start gap-3 h-12 text-left"
                    onClick={() => onAdd(action.type)}
                  >
                    {action.icon}
                    <span>{action.label}</span>
                  </Button>
                </DrawerClose>
              ))}
            </div>
          </DrawerContent>
        </Drawer>
      </div>
    </>
  )
}