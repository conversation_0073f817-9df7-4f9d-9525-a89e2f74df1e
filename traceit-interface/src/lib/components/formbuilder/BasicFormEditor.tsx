import React from 'react'
import { FormSchema, FormItem, FormField, FormCategory, FieldType } from './types'
import { BasicFormItem } from './BasicFormItem'
import { toast } from 'sonner'
import { getFieldDefaults } from './utils'

interface BasicFormEditorProps {
  schema: FormSchema
  onSchemaChange: (schema: FormSchema) => void
  fieldLimit: number
  currentFieldCount: number
}

export const BasicFormEditor: React.FC<BasicFormEditorProps> = ({
  schema,
  onSchemaChange,
  fieldLimit,
  currentFieldCount
}) => {
  const handleUpdateItem = (id: string, updates: Partial<FormItem>) => {
    const updateItemsRecursively = (items: FormItem[]): FormItem[] => {
      return items.map(item => {
        if (item.id === id) {
          return { ...item, ...updates }
        }
        if ('children' in item && item.children) {
          return {
            ...item,
            children: updateItemsRecursively(item.children)
          }
        }
        return item
      })
    }
    
    onSchemaChange({ ...schema, items: updateItemsRecursively(schema.items) })
  }

  const handleDeleteItem = (id: string) => {
    const deleteItemRecursively = (items: FormItem[]): FormItem[] => {
      return items.filter(item => {
        if (item.id === id) {
          return false
        }
        if ('children' in item && item.children) {
          item.children = deleteItemRecursively(item.children)
        }
        return true
      })
    }
    
    onSchemaChange({ ...schema, items: deleteItemRecursively(schema.items) })
    toast.success('Item deleted')
  }

  const handleAddField = (parentId?: string, fieldType?: FieldType) => {
    if (currentFieldCount >= fieldLimit) {
      toast.error(`Field limit reached (${fieldLimit} fields maximum)`)
      return
    }

    const defaults = getFieldDefaults(fieldType || 'string')
    const newField: FormField = {
      id: `field_${Date.now()}`,
      name: defaults.name,
      type: fieldType || 'string',
      value: defaults.value,
      placeholder: defaults.placeholder,
      level: parentId ? 1 : 0,
    }

    if (parentId) {
      const findItem = (items: FormItem[], targetId: string): FormItem | null => {
        for (const item of items) {
          if (item.id === targetId) return item
          if ('children' in item && item.children) {
            const found = findItem(item.children, targetId)
            if (found) return found
          }
        }
        return null
      }

      const parentItem = findItem(schema.items, parentId)
      
      if (parentItem && 'children' in parentItem) {
        const addToCategory = (items: FormItem[]): FormItem[] => {
          return items.map(item => {
            if (item.id === parentId && 'children' in item) {
              return {
                ...item,
                children: [...item.children, { ...newField, level: item.level + 1 }]
              }
            }
            if ('children' in item && item.children) {
              return {
                ...item,
                children: addToCategory(item.children)
              }
            }
            return item
          })
        }
        onSchemaChange({ ...schema, items: addToCategory(schema.items) })
      } else {
        const addAfterField = (items: FormItem[]): FormItem[] => {
          const result: FormItem[] = []
          items.forEach(item => {
            result.push(item)
            if (item.id === parentId) {
              result.push({ ...newField, level: item.level })
            }
            if ('children' in item && item.children) {
              result[result.length - 1] = {
                ...item,
                children: addAfterField(item.children)
              }
            }
          })
          return result
        }
        onSchemaChange({ ...schema, items: addAfterField(schema.items) })
      }
    } else {
      onSchemaChange({ ...schema, items: [...schema.items, newField] })
    }
    
    toast.success('Field added')
  }

  const handleAddCategory = (parentId?: string) => {
    const newCategory: FormCategory = {
      id: `category_${Date.now()}`,
      name: 'new_section',
      level: parentId ? 1 : 0,
      children: []
    }
    
    if (parentId) {
      const findItem = (items: FormItem[], targetId: string): FormItem | null => {
        for (const item of items) {
          if (item.id === targetId) return item
          if ('children' in item && item.children) {
            const found = findItem(item.children, targetId)
            if (found) return found
          }
        }
        return null
      }

      const parentItem = findItem(schema.items, parentId)
      
      if (parentItem && 'children' in parentItem) {
        const addToCategory = (items: FormItem[]): FormItem[] => {
          return items.map(item => {
            if (item.id === parentId && 'children' in item) {
              return {
                ...item,
                children: [...item.children, { ...newCategory, level: item.level + 1 }]
              }
            }
            if ('children' in item && item.children) {
              return {
                ...item,
                children: addToCategory(item.children)
              }
            }
            return item
          })
        }
        onSchemaChange({ ...schema, items: addToCategory(schema.items) })
      } else {
        const addAfterField = (items: FormItem[]): FormItem[] => {
          const result: FormItem[] = []
          items.forEach(item => {
            result.push(item)
            if (item.id === parentId) {
              result.push({ ...newCategory, level: item.level })
            }
            if ('children' in item && item.children) {
              result[result.length - 1] = {
                ...item,
                children: addAfterField(item.children)
              }
            }
          })
          return result
        }
        onSchemaChange({ ...schema, items: addAfterField(schema.items) })
      }
    } else {
      onSchemaChange({ ...schema, items: [...schema.items, newCategory] })
    }
    
    toast.success('Section added')
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-y-auto px-4 py-3 space-y-2">
        {schema.items.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <p className="text-muted-foreground">Empty form - hover over items to add fields</p>
          </div>
        ) : (
          schema.items.map((item) => (
            <BasicFormItem
              key={item.id}
              item={item}
              onUpdate={handleUpdateItem}
              onDelete={handleDeleteItem}
              onAddField={handleAddField}
              onAddCategory={handleAddCategory}
              allItems={schema.items}
            />
          ))
        )}
      </div>
    </div>
  )
}