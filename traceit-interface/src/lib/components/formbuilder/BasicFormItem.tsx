import React, { useState } from 'react'
import { Card } from '@/lib/components/ui/card'
import { Button } from '@/lib/components/ui/button'
import { Input } from '@/lib/components/ui/input'
import { Textarea } from '@/lib/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/lib/components/ui/select'
import { InlineAddButton } from './InlineAddButton'
import { 
  Trash2, 
  Calendar,
  Type,
  Hash,
  Link,
  Mail,
  MapPin,
  Clock,
  AlignLeft,
  ChevronDown,
  ChevronRight
} from 'lucide-react'
import { FormItem, FormField, FormCategory, FieldType } from './types'
import { cn } from '@/lib/utils'
import { sanitizeFieldName, isValidJsonKey } from './utils'
import { toast } from 'sonner'

interface BasicFormItemProps {
  item: FormItem
  onUpdate: (id: string, updates: Partial<FormItem>) => void
  onDelete: (id: string) => void
  onAddField: (parentId?: string, fieldType?: FieldType) => void
  onAddCategory?: (parentId?: string) => void
  allItems?: FormItem[]
}

const FIELD_TYPE_CONFIG: Record<FieldType, { icon: React.ReactNode; label: string; inputType?: string }> = {
  string: { icon: <Type className="w-4 h-4" />, label: 'Text' },
  textarea: { icon: <AlignLeft className="w-4 h-4" />, label: 'Long Text' },
  number: { icon: <Hash className="w-4 h-4" />, label: 'Number', inputType: 'number' },
  date: { icon: <Calendar className="w-4 h-4" />, label: 'Date', inputType: 'date' },
  timestamp: { icon: <Clock className="w-4 h-4" />, label: 'Timestamp', inputType: 'datetime-local' },
  geolocation: { icon: <MapPin className="w-4 h-4" />, label: 'Location' },
  url: { icon: <Link className="w-4 h-4" />, label: 'URL', inputType: 'url' },
  email: { icon: <Mail className="w-4 h-4" />, label: 'Email', inputType: 'email' },
  array: { icon: <Type className="w-4 h-4" />, label: 'List' }
}


export const BasicFormItem: React.FC<BasicFormItemProps> = ({
  item,
  onUpdate,
  onDelete,
  onAddField,
  onAddCategory,
  allItems
}) => {
  const [isEditingName, setIsEditingName] = useState(false)
  const [editName, setEditName] = useState(item.name)
  const [isCollapsed, setIsCollapsed] = useState(false)

  const isCategory = 'children' in item

  const checkForDuplicatesAtSameLevel = (items: FormItem[], currentItem: FormItem): boolean => {
    if (!items) return false
    
    const findSiblings = (items: FormItem[], targetItem: FormItem, parentLevel = 0): FormItem[] => {
      for (const item of items) {
        if (item.id === targetItem.id) {
          return items.filter(sibling => sibling.id !== targetItem.id && sibling.level === targetItem.level)
        }
        if ('children' in item && item.children) {
          const foundSiblings = findSiblings(item.children, targetItem, item.level + 1)
          if (foundSiblings.length > 0 || item.children.some(child => child.id === targetItem.id)) {
            return foundSiblings.length > 0 ? foundSiblings : item.children.filter(sibling => sibling.id !== targetItem.id)
          }
        }
      }
      return []
    }
    
    const siblings = findSiblings(items, currentItem)
    return siblings.some(sibling => sibling.name === currentItem.name)
  }

  const isDuplicate = allItems ? checkForDuplicatesAtSameLevel(allItems, item) : false

  const handleNameSave = () => {
    const trimmedName = editName.trim()
    if (trimmedName) {
      const sanitizedName = sanitizeFieldName(trimmedName)
      onUpdate(item.id, { name: sanitizedName })
      setEditName(sanitizedName) // Update the edit state with sanitized name
    }
    setIsEditingName(false)
  }

  const handleNameCancel = () => {
    setEditName(item.name)
    setIsEditingName(false)
  }

  const handleTypeChange = (newType: FieldType) => {
    if (!isCategory) {
      onUpdate(item.id, { type: newType } as Partial<FormField>)
    }
  }

  const handleSpecialInputs = async (fieldType: FieldType) => {
    if (fieldType === 'timestamp') {
      const now = new Date().toISOString()
      onUpdate(item.id, { value: now })
      toast.success('Timestamp updated')
    } else if (fieldType === 'geolocation') {
      try {
        if (navigator.geolocation) {
          toast.info('Requesting location permission...')
          const position = await new Promise<GeolocationPosition>((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 60000
            })
          })
          const location = `${position.coords.latitude}, ${position.coords.longitude}`
          onUpdate(item.id, { value: location })
          toast.success(`Location updated: ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}`)
        } else {
          toast.error('Geolocation is not supported by this browser')
        }
      } catch (error) {
        toast.error('Location access denied or unavailable')
      }
    }
  }

  const handleAddFieldType = async (fieldType: FieldType | 'category') => {
    if (fieldType === 'category') {
      onAddCategory?.(item.id)
    } else {
      onAddField(item.id, fieldType)
      
      if (fieldType === 'geolocation') {
        try {
          if (navigator.geolocation) {
            toast.info('Requesting location permission...')
            const position = await new Promise<GeolocationPosition>((resolve, reject) => {
              navigator.geolocation.getCurrentPosition(resolve, reject, {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
              })
            })
            toast.success(`Location acquired: ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}`)
          } else {
            toast.error('Geolocation is not supported by this browser')
          }
        } catch {
          toast.error('Location access denied or unavailable')
        }
      }
    }
  }

  const renderFieldInput = () => {
    if (isCategory) return null
    
    const field = item as FormField
    const config = FIELD_TYPE_CONFIG[field.type]
    
    if (field.type === 'textarea') {
      return (
        <Textarea
          value={field.value}
          onChange={(e) => onUpdate(item.id, { value: e.target.value })}
          placeholder={field.placeholder || "Enter text..."}
          className="min-h-[32px] h-8 resize-none"
          rows={1}
        />
      )
    }

    if (field.type === 'timestamp') {
      return (
        <div className="flex gap-2">
          <Input
            type="datetime-local"
            value={field.value}
            onChange={(e) => onUpdate(item.id, { value: e.target.value })}
            className="flex-1 h-8"
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => handleSpecialInputs('timestamp')}
            className="h-8 px-2"
          >
            Now
          </Button>
        </div>
      )
    }

    if (field.type === 'geolocation') {
      return (
        <div className="flex gap-2">
          <Input
            type="text"
            value={field.value}
            onChange={(e) => onUpdate(item.id, { value: e.target.value })}
            placeholder={field.placeholder || "lat, lng"}
            className="flex-1 h-8"
          />
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => handleSpecialInputs('geolocation')}
            className="h-8 px-2"
          >
            Get
          </Button>
        </div>
      )
    }
    
    return (
      <Input
        type={field.type === 'date' ? 'datetime-local' : config.inputType || 'text'}
        value={field.value}
        onChange={(e) => onUpdate(item.id, { value: e.target.value })}
        placeholder={field.placeholder || `Enter ${config.label.toLowerCase()}...`}
        className="h-8"
      />
    )
  }

  return (
    <div className={cn(
      "group", 
      item.level > 0 && "ml-0 md:ml-8",
      item.level > 0 && "border-l-2 border-l-primary/20 md:border-l-0 pl-3 md:pl-0"
    )}>
      <Card className={cn(
        "transition-all hover:shadow-sm",
        "border-0 md:border md:border-border/40 md:bg-card/60",
        "bg-transparent md:bg-card/60",
        isCategory && "md:border-l-2 md:border-l-border",
        "md:hover:border-border/60",
        item.level > 0 && "md:bg-card/40",
        "border-b border-border/20 md:border-b-0 rounded-none md:rounded-lg"
      )}>
        <div className={cn(
          "py-2 px-0 md:p-3",
          item.level > 0 && "py-1.5 md:p-3"
        )}>
          {!isCategory ? (
            <div className="space-y-3">
              <div className="md:hidden space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div className="text-muted-foreground flex-shrink-0">
                      {FIELD_TYPE_CONFIG[(item as FormField).type].icon}
                    </div>
                    
                    {isEditingName ? (
                      <div className="flex items-center gap-1 flex-1">
                        <div className="flex-1 relative">
                          <Input
                            value={editName}
                            onChange={(e) => setEditName(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') handleNameSave()
                              if (e.key === 'Escape') handleNameCancel()
                            }}
                            className={cn(
                              "h-8 text-sm pr-6",
                              !isValidJsonKey(editName) && editName.trim() && "border-orange-500"
                            )}
                            autoFocus
                          />
                          {editName.trim() && !isValidJsonKey(editName) && (
                            <div className="absolute right-1 top-1/2 -translate-y-1/2 text-xs text-orange-500">
                              {sanitizeFieldName(editName).slice(0, 6)}...
                            </div>
                          )}
                        </div>
                        <Button size="sm" variant="ghost" onClick={handleNameSave} className="h-8 w-8 p-0 text-xs">
                          ✓
                        </Button>
                        <Button size="sm" variant="ghost" onClick={handleNameCancel} className="h-8 w-8 p-0 text-xs">
                          ✕
                        </Button>
                      </div>
                    ) : (
                      <button
                        onClick={() => setIsEditingName(true)}
                        className={cn(
                          "font-medium hover:text-primary transition-colors text-left text-sm flex-1 truncate",
                          isDuplicate && "text-destructive bg-destructive/10 px-2 py-0.5 rounded border border-destructive/20"
                        )}
                      >
                        {item.name}
                        {isDuplicate && <span className="ml-1 text-xs">⚠</span>}
                      </button>
                    )}
                  </div>

                  {!isEditingName && (
                    <div className="flex items-center gap-1 flex-shrink-0">
                      <InlineAddButton 
                        onAdd={handleAddFieldType}
                        className="opacity-70"
                      />
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => onDelete(item.id)}
                        className="text-muted-foreground hover:text-destructive hover:bg-destructive/10 h-8 w-8 p-0 transition-colors opacity-70"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  )}
                </div>

                {!isEditingName && (
                  <div className="flex gap-2">
                    <Select
                      value={(item as FormField).type}
                      onValueChange={(value) => handleTypeChange(value as FieldType)}
                    >
                      <SelectTrigger className="w-24 h-8 text-xs">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(FIELD_TYPE_CONFIG).map(([type, config]) => (
                          <SelectItem key={type} value={type} className="text-xs">
                            <div className="flex items-center gap-2">
                              {config.icon}
                              <span>{config.label}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <div className="flex-1">
                      {renderFieldInput()}
                    </div>
                  </div>
                )}
              </div>

              <div className="hidden md:flex md:items-center md:gap-3 md:-mt-2">
                <div className="text-muted-foreground flex-shrink-0">
                  {FIELD_TYPE_CONFIG[(item as FormField).type].icon}
                </div>

                {isEditingName ? (
                  <div className="flex items-center gap-1 w-48">
                    <div className="flex-1 relative">
                      <Input
                        value={editName}
                        onChange={(e) => setEditName(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') handleNameSave()
                          if (e.key === 'Escape') handleNameCancel()
                        }}
                        className={cn(
                          "h-7 text-sm pr-6",
                          !isValidJsonKey(editName) && editName.trim() && "border-orange-500"
                        )}
                        autoFocus
                      />
                      {editName.trim() && !isValidJsonKey(editName) && (
                        <div className="absolute right-1 top-1/2 -translate-y-1/2 text-xs text-orange-500">
                          {sanitizeFieldName(editName).slice(0, 6)}...
                        </div>
                      )}
                    </div>
                    <Button size="sm" variant="ghost" onClick={handleNameSave} className="h-7 w-7 p-0 text-xs">
                      ✓
                    </Button>
                    <Button size="sm" variant="ghost" onClick={handleNameCancel} className="h-7 w-7 p-0 text-xs">
                      ✕
                    </Button>
                  </div>
                ) : (
                  <button
                    onClick={() => setIsEditingName(true)}
                    className={cn(
                      "font-medium hover:text-primary transition-colors text-left text-sm w-48 truncate",
                      isDuplicate && "text-destructive bg-destructive/10 px-2 py-0.5 rounded border border-destructive/20"
                    )}
                  >
                    {item.name}
                    {isDuplicate && <span className="ml-1 text-xs">⚠</span>}
                  </button>
                )}

                {!isEditingName && (
                  <Select
                    value={(item as FormField).type}
                    onValueChange={(value) => handleTypeChange(value as FieldType)}
                  >
                    <SelectTrigger className="w-28 h-7 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(FIELD_TYPE_CONFIG).map(([type, config]) => (
                        <SelectItem key={type} value={type} className="text-xs">
                          <div className="flex items-center gap-2">
                            {config.icon}
                            <span>{config.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}

                {!isEditingName && (
                  <div className="flex-1">
                    {renderFieldInput()}
                  </div>
                )}

                {!isEditingName && (
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100">
                    <InlineAddButton 
                      onAdd={handleAddFieldType}
                      className="md:opacity-0 md:group-hover:opacity-100"
                    />

                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => onDelete(item.id)}
                      className="text-muted-foreground hover:text-destructive hover:bg-destructive/10 h-7 w-7 p-0 transition-colors"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="md:hidden">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <button
                      onClick={() => setIsCollapsed(!isCollapsed)}
                      className="p-0.5 hover:bg-muted rounded flex-shrink-0"
                    >
                      {isCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                    </button>

                    {isEditingName ? (
                      <div className="flex items-center gap-1 flex-1">
                        <div className="flex-1 relative">
                          <Input
                            value={editName}
                            onChange={(e) => setEditName(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') handleNameSave()
                              if (e.key === 'Escape') handleNameCancel()
                            }}
                            className={cn(
                              "h-8 font-semibold pr-6",
                              !isValidJsonKey(editName) && editName.trim() && "border-orange-500"
                            )}
                            autoFocus
                          />
                          {editName.trim() && !isValidJsonKey(editName) && (
                            <div className="absolute right-1 top-1/2 -translate-y-1/2 text-xs text-orange-500">
                              {sanitizeFieldName(editName).slice(0, 6)}...
                            </div>
                          )}
                        </div>
                        <Button size="sm" variant="ghost" onClick={handleNameSave} className="h-8 w-8 p-0">
                          ✓
                        </Button>
                        <Button size="sm" variant="ghost" onClick={handleNameCancel} className="h-8 w-8 p-0">
                          ✕
                        </Button>
                      </div>
                    ) : (
                      <button
                        onClick={() => setIsEditingName(true)}
                        className={cn(
                          "font-medium hover:text-primary transition-colors text-left flex-1 truncate text-sm",
                          isDuplicate && "text-destructive bg-destructive/10 px-2 py-0.5 rounded border border-destructive/20"
                        )}
                      >
                        {item.name}
                        {isDuplicate && <span className="ml-1 text-xs">⚠</span>}
                      </button>
                    )}
                  </div>

                  {!isEditingName && (
                    <div className="flex items-center gap-1 flex-shrink-0">
                      <InlineAddButton 
                        onAdd={handleAddFieldType}
                        className="opacity-70"
                      />
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => onDelete(item.id)}
                        className="text-destructive hover:text-destructive hover:bg-destructive/10 h-8 w-8 p-0 flex-shrink-0 opacity-70"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              <div className="hidden md:flex md:items-center md:gap-2 md:flex-wrap">
                <button
                  onClick={() => setIsCollapsed(!isCollapsed)}
                  className="p-1 hover:bg-muted rounded"
                >
                  {isCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                </button>

                {isEditingName ? (
                  <div className="flex items-center gap-2 flex-1">
                    <div className="flex-1 relative">
                      <Input
                        value={editName}
                        onChange={(e) => setEditName(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') handleNameSave()
                          if (e.key === 'Escape') handleNameCancel()
                        }}
                        className={cn(
                          "h-8 font-semibold pr-6",
                          !isValidJsonKey(editName) && editName.trim() && "border-orange-500"
                        )}
                        autoFocus
                      />
                      {editName.trim() && !isValidJsonKey(editName) && (
                        <div className="absolute right-1 top-1/2 -translate-y-1/2 text-xs text-orange-500">
                          {sanitizeFieldName(editName).slice(0, 6)}...
                        </div>
                      )}
                    </div>
                    <Button size="sm" variant="ghost" onClick={handleNameSave} className="h-8 w-8 p-0">
                      ✓
                    </Button>
                    <Button size="sm" variant="ghost" onClick={handleNameCancel} className="h-8 w-8 p-0">
                      ✕
                    </Button>
                  </div>
                ) : (
                  <button
                    onClick={() => setIsEditingName(true)}
                    className={cn(
                      "font-semibold hover:text-primary transition-colors text-left flex-1",
                      isDuplicate && "text-destructive bg-destructive/10 px-2 py-0.5 rounded border border-destructive/20"
                    )}
                  >
                    {item.name}
                    {isDuplicate && <span className="ml-1 text-xs">⚠</span>}
                  </button>
                )}

                {!isEditingName && (
                  <InlineAddButton 
                    onAdd={handleAddFieldType}
                    className="md:opacity-0 md:group-hover:opacity-100 transition-opacity flex-shrink-0"
                  />
                )}

                {!isEditingName && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onDelete(item.id)}
                    className="md:opacity-0 md:group-hover:opacity-100 transition-opacity text-destructive hover:text-destructive h-8 w-8 p-0 flex-shrink-0"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                )}
              </div>

              {!isCollapsed && (
                <div className="ml-0 md:ml-4 space-y-0.5 mt-2 md:mt-1">
                  {(item as FormCategory).children?.map((child) => (
                    <BasicFormItem
                      key={child.id}
                      item={child}
                      onUpdate={onUpdate}
                      onDelete={onDelete}
                      onAddField={onAddField}
                      onAddCategory={onAddCategory}
                      allItems={allItems}
                    />
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </Card>
    </div>
  )
}