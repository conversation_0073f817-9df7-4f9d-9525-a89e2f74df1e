import React, { useState } from 'react'
import { Plus, Folder, Type, Hash, Calendar, Link, Mail, AlignLeft, Clock, MapPin } from 'lucide-react'
import { Button } from '@/lib/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/lib/components/ui/popover'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/lib/components/ui/drawer'
import { FieldType } from './types'

interface InlineAddButtonProps {
  onAdd: (fieldType: FieldType | 'category') => void
  className?: string
}

const FIELD_TYPE_OPTIONS = [
  { id: 'category', label: 'Category', icon: <Folder className="w-4 h-4" /> },
  { id: 'string', label: 'Text', icon: <Type className="w-4 h-4" /> },
  { id: 'textarea', label: 'Long Text', icon: <AlignLeft className="w-4 h-4" /> },
  { id: 'number', label: 'Number', icon: <Hash className="w-4 h-4" /> },
  { id: 'date', label: 'Date', icon: <Calendar className="w-4 h-4" /> },
  { id: 'timestamp', label: 'Timestamp', icon: <Clock className="w-4 h-4" /> },
  { id: 'geolocation', label: 'Geolocation', icon: <MapPin className="w-4 h-4" /> },
  { id: 'url', label: 'URL', icon: <Link className="w-4 h-4" /> },
  { id: 'email', label: 'Email', icon: <Mail className="w-4 h-4" /> },
]

export const InlineAddButton: React.FC<InlineAddButtonProps> = ({ onAdd, className = "" }) => {
  const [popoverOpen, setPopoverOpen] = useState(false)
  const [drawerOpen, setDrawerOpen] = useState(false)

  const handleAdd = (fieldType: FieldType | 'category') => {
    onAdd(fieldType)
    setPopoverOpen(false)
    setDrawerOpen(false)
  }

  return (
    <>
      <div className="hidden md:block">
        <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
          <PopoverTrigger asChild>
            <Button
              size="sm"
              variant="ghost"
              className={`text-muted-foreground hover:text-primary h-8 w-8 p-0 ${className}`}
            >
              <Plus className="w-3 h-3" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-40 p-1" align="end">
            <div className="space-y-px">
              {FIELD_TYPE_OPTIONS.map((option) => (
                <button
                  key={option.id}
                  onClick={() => handleAdd(option.id as FieldType | 'category')}
                  className="w-full flex items-center gap-2 px-2 py-1.5 text-xs hover:bg-muted/70 active:bg-muted transition-colors text-left rounded-sm"
                >
                  <div className="text-muted-foreground">
                    {option.icon}
                  </div>
                  {option.label}
                </button>
              ))}
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <div className="block md:hidden">
        <Drawer open={drawerOpen} onOpenChange={setDrawerOpen}>
          <DrawerTrigger asChild>
            <Button
              size="sm"
              variant="ghost"
              className={`text-muted-foreground hover:text-primary h-8 w-8 p-0 ${className}`}
            >
              <Plus className="w-3 h-3" />
            </Button>
          </DrawerTrigger>
          <DrawerContent>
            <DrawerHeader>
              <DrawerTitle>Add Field</DrawerTitle>
            </DrawerHeader>
            <div className="px-4 pb-4 space-y-2">
              {FIELD_TYPE_OPTIONS.map((option) => (
                <DrawerClose key={option.id} asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-start gap-3 h-12 text-left"
                    onClick={() => handleAdd(option.id as FieldType | 'category')}
                  >
                    <div className="text-muted-foreground">
                      {option.icon}
                    </div>
                    <span>{option.label}</span>
                  </Button>
                </DrawerClose>
              ))}
            </div>
          </DrawerContent>
        </Drawer>
      </div>
    </>
  )
}