import React, { useState, useRef } from 'react'
import { Button } from '@/lib/components/ui/button'
import { StepActions } from '@/lib/components/ui/step-actions'
import { FormEditor } from './FormEditor'
import { BasicFormEditor } from './BasicFormEditor'
import { QuickActionsPopover } from './QuickActionsPopover'
import { FormSchema, JsonData, FieldType, FormField, FormItem } from './types'
import { schemaToJson, jsonToSchema, createEmptyCategory, generateId, validateFormSchema, ValidationResult, calculateJsonSize } from './utils'
import { toast } from 'sonner'
import { Code2, Layers } from 'lucide-react'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'motion/react'

interface FormBuilderProps {
  initialData?: JsonData
  templateId?: string | null
  templateName?: string | null
  dataSource?: 'own' | 'template' | null
  onSubmit: (data: JsonData) => void
  onBack: () => void
}

export const FormBuilder: React.FC<FormBuilderProps> = ({
  initialData,
  templateId,
  templateName,
  dataSource,
  onSubmit,
  onBack
}) => {
  const [schema, setSchema] = useState<FormSchema>(() => {
    if (initialData) {
      return jsonToSchema(initialData)
    }
    return { items: [] }
  })
  const [viewMode, setViewMode] = useState<'basic' | 'advanced'>('basic')
  
  const hasRenderedBasic = useRef(false)
  const hasRenderedAdvanced = useRef(false)
  
  const validationResult: ValidationResult = validateFormSchema(schema)
  const { isValid, errors, warnings } = validationResult

  const jsonData = schemaToJson(schema)
  const jsonSize = calculateJsonSize(jsonData)

  const handleSubmit = () => {
    const validation = validateFormSchema(schema)
    
    if (!validation.isValid) {
      if (validation.errors.length > 0) {
        toast.error(validation.errors[0].message)
      }
      return
    }
    
    if (validation.warnings.length > 0) {
      toast.warning(`${validation.warnings.length} warning(s) found, but continuing anyway.`)
    }
    
    onSubmit(jsonData)
  }

  const handleAdd = async (type: string) => {
    if (type !== 'category' && totalFieldCount >= fieldLimit) {
      toast.error(`Field limit reached (${fieldLimit} fields maximum)`)
      return
    }

    if (type === 'category') {
      const newCategory = createEmptyCategory('new_category', 0)
      setSchema({
        ...schema,
        items: [...schema.items, newCategory]
      })
      toast.success('Category added')
    } else {
      const fieldType = type === 'field' ? 'string' : type
      let fieldValue = ''
      
      if (fieldType === 'timestamp') {
        fieldValue = new Date().toISOString()
        toast.success(`Timestamp field added: ${new Date().toLocaleString()}`)
      } else if (fieldType === 'geolocation') {
        try {
          if (navigator.geolocation) {
            const position = await new Promise<GeolocationPosition>((resolve, reject) => {
              navigator.geolocation.getCurrentPosition(resolve, reject, {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
              })
            })
            fieldValue = `${position.coords.latitude}, ${position.coords.longitude}`
            toast.success(`Geolocation field added: ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}`)
          } else {
            toast.error('Geolocation not supported')
            return
          }
        } catch {
          toast.error('Location access denied or unavailable')
        }
      } else {
        toast.success('Field added')
      }
      
      const getFieldDefaults = (type: string) => {
        switch (type) {
          case 'string': return { name: 'text_field', value: 'sample text' }
          case 'textarea': return { name: 'description', value: 'long text content' }
          case 'number': return { name: 'count', value: '0' }
          case 'date': return { name: 'created_date', value: new Date().toISOString().split('T')[0] }
          case 'timestamp': return { name: 'timestamp', value: fieldValue }
          case 'geolocation': return { name: 'location', value: fieldValue }
          case 'url': return { name: 'website_url', value: 'https://example.com' }
          case 'email': return { name: 'email_address', value: '<EMAIL>' }
          case 'array': return { name: 'items', value: '[]' }
          default: return { name: 'new_field', value: '' }
        }
      }
      
      const defaults = getFieldDefaults(fieldType)
      const newField: FormField = {
        id: generateId(),
        name: defaults.name,
        type: fieldType as FieldType,
        value: fieldValue || defaults.value,
        level: 0,
        required: false
      }
      
      setSchema({
        ...schema,
        items: [...schema.items, newField]
      })
    }
  }

  const countFields = (items: FormItem[]): number => {
    return items.reduce((total, item) => {
      if ('type' in item) {
        return total + 1
      } else {
        return total + countFields(item.children || [])
      }
    }, 0)
  }
  
  const totalFieldCount = countFields(schema.items)
  const fieldLimit = 100

  return (
    <div className="max-w-6xl mx-auto min-h-[500px] md:min-h-[600px]">
      <div className="w-full min-h-[500px] md:min-h-[600px] bg-background border border-border rounded-lg flex flex-col">
        
        <div className="border-b border-border/30 px-2 md:px-4 py-3">
          <div className="flex flex-col gap-2 md:grid md:grid-cols-3 md:items-center">
            <div className="flex items-center gap-2 md:gap-3 justify-start flex-wrap">
              <div className={`text-xs font-mono ${
                totalFieldCount >= fieldLimit ? 'text-red-500' : 
                totalFieldCount >= fieldLimit * 0.8 ? 'text-orange-500' : 
                'text-muted-foreground/60'
              }`}>
                {totalFieldCount}/{fieldLimit} fields
              </div>
              
              <div className={`text-xs font-mono flex items-center gap-1 ${
                !isValid ? 'text-red-500' : 
                warnings.length > 0 ? 'text-yellow-600' : 
                'text-green-600'
              }`}>
                {!isValid ? (
                  <>⚠ {errors.length} error{errors.length !== 1 ? 's' : ''}</>
                ) : warnings.length > 0 ? (
                  <>⚠ {warnings.length} warning{warnings.length !== 1 ? 's' : ''}</>
                ) : (
                  <>✓ Valid</>
                )}
              </div>

              <div className="text-xs font-mono text-muted-foreground/60">
                {jsonSize}
              </div>
            </div>

            <div className="flex items-center justify-center order-last md:order-none">
              <div className="flex items-center bg-muted rounded-md p-0.5">
                <button
                  onClick={() => setViewMode('basic')}
                  className={cn(
                    "flex items-center gap-1.5 px-2 md:px-3 py-1.5 text-xs font-medium rounded transition-all",
                    viewMode === 'basic' 
                      ? "bg-background text-foreground shadow-sm" 
                      : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  <Layers className="w-3 h-3" />
                  Basic
                </button>
                <button
                  onClick={() => setViewMode('advanced')}
                  className={cn(
                    "flex items-center gap-1.5 px-2 md:px-3 py-1.5 text-xs font-medium rounded transition-all",
                    viewMode === 'advanced' 
                      ? "bg-background text-foreground shadow-sm" 
                      : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  <Code2 className="w-3 h-3" />
                  Advanced
                </button>
              </div>
            </div>

            <div className="flex items-center justify-end md:justify-end">
              <QuickActionsPopover 
                onAdd={handleAdd}
                fieldCount={totalFieldCount}
                fieldLimit={fieldLimit}
              />
            </div>
          </div>
        </div>

        <div className="flex-1 min-h-0">
          <AnimatePresence mode="wait">
            {viewMode === 'basic' ? (
              <motion.div
                key="basic-view"
                initial={!hasRenderedBasic.current ? { opacity: 0, y: 8 } : false}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -8 }}
                transition={{ 
                  duration: 0.3, 
                  ease: [0.21, 1.11, 0.81, 0.99],
                  delay: !hasRenderedBasic.current ? 0.1 : 0
                }}
                className="h-full"
                onAnimationComplete={() => { hasRenderedBasic.current = true }}
              >
                <BasicFormEditor 
                  schema={schema}
                  onSchemaChange={setSchema}
                  fieldLimit={fieldLimit}
                  currentFieldCount={totalFieldCount}
                />
              </motion.div>
            ) : (
              <motion.div
                key="advanced-view"
                initial={!hasRenderedAdvanced.current ? { opacity: 0, y: 8 } : false}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -8 }}
                transition={{ 
                  duration: 0.3, 
                  ease: [0.21, 1.11, 0.81, 0.99],
                  delay: !hasRenderedAdvanced.current ? 0.1 : 0
                }}
                className="h-full"
                onAnimationComplete={() => { hasRenderedAdvanced.current = true }}
              >
                <FormEditor 
                  schema={schema}
                  onSchemaChange={setSchema}
                  fieldLimit={fieldLimit}
                  currentFieldCount={totalFieldCount}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <StepActions
          onBack={onBack}
          onContinue={handleSubmit}
          continueDisabled={!isValid}
          backLabel="← Back"
          continueLabel="Continue"
        />
      </div>
    </div>
  )
}