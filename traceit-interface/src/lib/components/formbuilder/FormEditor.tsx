import React from 'react'
import { HierarchicalItem } from './HierarchicalItem'
import { FormSchema, FormField, FormItem, FieldType } from './types'
import { createEmptyCategory, generateId, getFieldDefaults } from './utils'
import { toast } from 'sonner'
import { GripVertical } from 'lucide-react'

interface FormEditorProps {
  schema: FormSchema
  onSchemaChange: (schema: FormSchema) => void
  fieldLimit?: number
  currentFieldCount?: number
}

export const FormEditor: React.FC<FormEditorProps> = ({
  schema,
  onSchemaChange,
  fieldLimit = 100,
  currentFieldCount = 0
}) => {
  const handleAdd = (type: string) => {
    if (type !== 'category' && currentFieldCount >= fieldLimit) {
      toast.error(`Field limit reached (${fieldLimit} fields maximum)`)
      return
    }

    if (type === 'category') {
      const newCategory = createEmptyCategory('new_category', 0)
      onSchemaChange({
        ...schema,
        items: [...schema.items, newCategory]
      })
    } else {
      const defaults = getFieldDefaults(type as FieldType)
      const newField: FormField = {
        id: generateId(),
        name: defaults.name,
        type: type as FieldType,
        value: defaults.value,
        placeholder: defaults.placeholder,
        level: 0,
        required: false
      }
      
      onSchemaChange({
        ...schema,
        items: [...schema.items, newField]
      })
    }
    
    toast.success(type === 'category' ? 'Category added' : 'Field added')
  }

  const handleAddField = (parentId: string, fieldType: FieldType | 'category' | 'field') => {
    if (fieldType !== 'category' && currentFieldCount >= fieldLimit) {
      toast.error(`Field limit reached (${fieldLimit} fields maximum)`)
      return
    }
    const addFieldToItem = (items: FormItem[], targetId: string): FormItem[] => {
      return items.map(item => {
        if (item.id === targetId) {
          if ('children' in item) {
            if (fieldType === 'category') {
              const newCategory = createEmptyCategory('new_category', item.level + 1)
              return {
                ...item,
                children: [...item.children, newCategory]
              }
            } else {
              const actualFieldType = fieldType === 'field' ? 'string' : fieldType
              const defaults = getFieldDefaults(actualFieldType as FieldType)
              const newField: FormField = {
                id: generateId(),
                name: defaults.name,
                type: actualFieldType as FieldType,
                value: defaults.value,
                placeholder: defaults.placeholder,
                level: item.level + 1,
                required: false
              }
              return {
                ...item,
                children: [...item.children, newField]
              }
            }
          } else {
            return item
          }
        }
        
        if ('children' in item) {
          return {
            ...item,
            children: addFieldToItem(item.children, targetId)
          }
        }
        
        return item
      })
    }

    const addAfterField = (items: FormItem[], targetId: string, level: number): FormItem[] => {
      const result: FormItem[] = []
      
      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        result.push(item)
        
        if (item.id === targetId) {
          if (fieldType === 'category') {
            const newCategory = createEmptyCategory('new_category', item.level)
            result.push(newCategory)
          } else {
            const actualFieldType = fieldType === 'field' ? 'string' : fieldType
            const defaults = getFieldDefaults(actualFieldType as FieldType)
            const newField: FormField = {
              id: generateId(),
              name: defaults.name,
              type: actualFieldType as FieldType,
              value: defaults.value,
              placeholder: defaults.placeholder,
              level: item.level,
              required: false
            }
            result.push(newField)
          }
        } else if ('children' in item) {
          result[result.length - 1] = {
            ...item,
            children: addAfterField(item.children, targetId, level)
          }
        }
      }
      
      return result
    }

    const findItem = (items: FormItem[], targetId: string): FormItem | null => {
      for (const item of items) {
        if (item.id === targetId) return item
        if ('children' in item) {
          const found = findItem(item.children, targetId)
          if (found) return found
        }
      }
      return null
    }
    
    const targetItem = findItem(schema.items, parentId)
    let updatedItems: FormItem[]
    
    if (targetItem && 'children' in targetItem) {
      updatedItems = addFieldToItem(schema.items, parentId)
    } else {
      updatedItems = addAfterField(schema.items, parentId, 0)
    }

    onSchemaChange({ ...schema, items: updatedItems })
    if (fieldType === 'category') {
      toast.success('Category added')
    } else {
      const fieldLabel = fieldType === 'string' ? 'Text field' : 
                        fieldType === 'textarea' ? 'Long text field' :
                        fieldType === 'number' ? 'Number field' :
                        fieldType === 'date' ? 'Date field' :
                        fieldType === 'timestamp' ? 'Timestamp field' :
                        fieldType === 'geolocation' ? 'Geolocation field' :
                        fieldType === 'url' ? 'URL field' :
                        fieldType === 'email' ? 'Email field' : 'Field'
      toast.success(`${fieldLabel} added`)
    }
  }

  const updateItemRecursively = (items: FormItem[], itemId: string, updates: Partial<FormItem>): FormItem[] => {
    return items.map(item => {
      if (item.id === itemId) {
        return { ...item, ...updates }
      }
      
      if ('children' in item) {
        return {
          ...item,
          children: updateItemRecursively(item.children, itemId, updates)
        }
      }
      
      return item
    })
  }

  const deleteItemRecursively = (items: FormItem[], itemId: string): FormItem[] => {
    return items.filter(item => {
      if (item.id === itemId) {
        return false
      }
      
      if ('children' in item) {
        return {
          ...item,
          children: deleteItemRecursively(item.children, itemId)
        }
      }
      
      return true
    }).map(item => {
      if ('children' in item) {
        return {
          ...item,
          children: deleteItemRecursively(item.children, itemId)
        }
      }
      return item
    })
  }

  const handleUpdateItem = (itemId: string, updates: Partial<FormItem>) => {
    const updatedItems = updateItemRecursively(schema.items, itemId, updates)
    onSchemaChange({ ...schema, items: updatedItems })
  }

  const handleDeleteItem = (itemId: string) => {
    const updatedItems = deleteItemRecursively(schema.items, itemId)
    onSchemaChange({ ...schema, items: updatedItems })
    toast.success('Item deleted')
  }

  const calculateLineNumbers = (items: FormItem[], startLine: number = 2): { [key: string]: number } => {
    const lineNumbers: { [key: string]: number } = {}
    let currentLine = startLine
    
    items.forEach(item => {
      lineNumbers[item.id] = currentLine
      currentLine += 1
      
      if ('children' in item && item.children.length > 0) {
        const childLineNumbers = calculateLineNumbers(item.children, currentLine)
        Object.assign(lineNumbers, childLineNumbers)
        currentLine += Object.keys(childLineNumbers).length + 1
      }
    })
    
    return lineNumbers
  }
  
  const lineNumbers = calculateLineNumbers(schema.items)
  const totalLines = Object.keys(lineNumbers).length + 2

  return (
    <div className="h-full overflow-auto">
      <div className="px-1 py-2 md:px-2">
        <div className="font-mono text-xs md:text-sm leading-6">
          <div className="flex items-center py-0.5 hover:bg-muted/20 transition-colors">
            <div className="w-4 flex items-center justify-center text-muted-foreground/20">
              <GripVertical className="w-3 h-3" />
            </div>
            
            <div className="w-8 md:w-10 text-xs text-muted-foreground/40 text-right pr-2 md:pr-3 font-mono select-none">
              1
            </div>
            <div className="flex items-center">
              <span className="text-muted-foreground/60 select-none">{'{'}</span>
            </div>
          </div>

          {schema.items.length > 0 ? (
            <div>
              {schema.items.map((item, index) => (
                <HierarchicalItem
                  key={item.id}
                  item={item}
                  onUpdate={handleUpdateItem}
                  onDelete={handleDeleteItem}
                  onAddField={handleAddField}
                  isLast={index === schema.items.length - 1}
                  lineNumber={lineNumbers[item.id] || index + 2}
                  allItems={schema.items}
                />
              ))}
            </div>
          ) : (
            <div className="flex items-center py-0.5">
                <div className="w-4 flex items-center justify-center text-muted-foreground/20">
                <GripVertical className="w-3 h-3" />
              </div>
              
              <div className="w-8 md:w-10 text-xs text-muted-foreground/40 text-right pr-2 md:pr-3 font-mono select-none">
                2
              </div>
              <div className="flex items-center font-mono text-xs md:text-sm">
                <div className="select-none" style={{ width: '1rem' }}></div>
                <div className="text-muted-foreground/40 italic text-xs">
Empty object - add fields to get started
                </div>
              </div>
            </div>
          )}

          <div className="flex items-center py-0.5 hover:bg-muted/20 transition-colors">
            <div className="w-4 flex items-center justify-center text-muted-foreground/20">
              <GripVertical className="w-3 h-3" />
            </div>
            
            <div className="w-8 md:w-10 text-xs text-muted-foreground/40 text-right pr-2 md:pr-3 font-mono select-none">
              {schema.items.length > 0 ? totalLines : 3}
            </div>
            <div className="flex items-center">
              <span className="text-muted-foreground/60 select-none">{'}'}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}