import React, { useState } from 'react'
import { But<PERSON> } from '@/lib/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/lib/components/ui/popover'
import { Plus, Folder, Type, Calendar, Link, Hash, Mail, AlignLeft } from 'lucide-react'
import { toast } from 'sonner'

interface AddOption {
  id: string
  label: string
  icon: React.ReactNode
  description: string
}

const HEADER_OPTIONS: AddOption[] = [
  {
    id: 'field',
    label: 'Add Field',
    icon: <Type className="w-4 h-4" />,
    description: 'Add a new field'
  },
  {
    id: 'category',
    label: 'Add Category',
    icon: <Folder className="w-4 h-4" />,
    description: 'Add a new category'
  }
]

const ADD_OPTIONS: AddOption[] = [
  {
    id: 'category',
    label: 'Category',
    icon: <Folder className="w-4 h-4" />,
    description: 'Group of related fields'
  },
  {
    id: 'field',
    label: 'Text Field',
    icon: <Type className="w-4 h-4" />,
    description: 'Simple text input'
  },
  {
    id: 'textarea',
    label: 'Long Text',
    icon: <AlignLeft className="w-4 h-4" />,
    description: 'Multi-line text'
  },
  {
    id: 'number',
    label: 'Number',
    icon: <Hash className="w-4 h-4" />,
    description: 'Numeric input'
  },
  {
    id: 'date',
    label: 'Date',
    icon: <Calendar className="w-4 h-4" />,
    description: 'Date picker'
  },
  {
    id: 'url',
    label: 'URL',
    icon: <Link className="w-4 h-4" />,
    description: 'Web address'
  },
  {
    id: 'email',
    label: 'Email',
    icon: <Mail className="w-4 h-4" />,
    description: 'Email address'
  }
]

interface AddButtonProps {
  onAdd: (type: string) => void
  isHeader?: boolean
}

export const AddButton: React.FC<AddButtonProps> = ({ onAdd, isHeader = false }) => {
  const [open, setOpen] = useState(false)

  const handleSelect = (option: AddOption) => {
    onAdd(option.id)
    setOpen(false)
    if (!isHeader) {
      toast.success(`${option.label} added`)
    }
  }

  const options = isHeader ? HEADER_OPTIONS : ADD_OPTIONS

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild className="flex w-full">
        <Button 
          variant="outline" 
          size="sm"
          className="flex text-xs border-border/40 hover:border-border hover:bg-muted/20 transition-all duration-200 font-normal"
        >
          <Plus className="w-3 h-3 mr-1" />
          Add
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-0" align="start">
        <div className="py-1">
          {options.map((option) => (
            <button
              key={option.id}
              onClick={() => handleSelect(option)}
              className="w-full flex items-center gap-3 px-3 py-2 hover:bg-muted/50 transition-colors text-left text-sm"
            >
              <div className="text-muted-foreground">
                {option.icon}
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium">{option.label}</div>
                <div className="text-xs text-muted-foreground/70 mt-0.5">{option.description}</div>
              </div>
            </button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  )
}