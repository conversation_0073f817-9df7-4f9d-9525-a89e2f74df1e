import { FormSchema, JsonData, FormCategory, FormField, FormItem, FieldType } from './types'

export const schemaToJson = (schema: FormSchema): JsonData => {
  const result: JsonData = {}
  
  const processItems = (items: FormItem[], target: any) => {
    items.forEach(item => {
      if ('type' in item) {
        if (item.type === 'array') {
          target[item.name] = item.value 
            ? item.value.split(',').map(v => v.trim()).filter(v => v)
            : []
        } else {
          target[item.name] = item.value || ''
        }
      } else {
        const categoryData: any = {}
        processItems(item.children, categoryData)
        target[item.name] = categoryData
      }
    })
  }
  
  processItems(schema.items, result)
  return result
}

export const jsonToSchema = (json: JsonData): FormSchema => {
  const processLevel = (data: any, level = 0): FormItem[] => {
    const result: FormItem[] = []
    
    Object.entries(data).forEach(([key, value]) => {
      if (isPrimitiveValue(value)) {
        result.push({
          id: generateId(),
          name: key,
          type: inferFieldType(value),
          value: '', // Always start with empty value
          placeholder: Array.isArray(value) ? value.join(', ') : String(value || ''), // Use actual value as placeholder
          level,
          required: false
        })
      } else if (Array.isArray(value) && value.length > 0) {
        const template = value[0]
        if (typeof template === 'object' && template !== null) {
          const children = processLevel(template, level + 1)
          result.push({
            id: generateId(),
            name: key,
            level,
            children,
            isCollapsed: false
          })
        }
      } else if (typeof value === 'object' && value !== null) {
        const children = processLevel(value, level + 1)
        result.push({
          id: generateId(),
          name: key,
          level,
          children,
          isCollapsed: false
        })
      }
    })
    
    return result
  }
  
  const items = processLevel(json)
  return { items }
}

const extractFieldsFromArray = (arr: any[]): FormField[] => {
  const fields: FormField[] = []
  const fieldMap = new Map<string, any>()
  
  arr.forEach(item => {
    if (typeof item === 'object' && item !== null) {
      extractAllFields(item, '', fieldMap)
    }
  })
  
  fieldMap.forEach((value, path) => {
    fields.push({
      id: generateId(),
      name: path,
      type: inferFieldType(value),
      value: '', // Always start with empty value
      placeholder: Array.isArray(value) ? value.join(', ') : String(value || ''), // Use actual value as placeholder
      level: 0,
      required: false
    })
  })
  
  return fields
}

const extractFieldsFromObject = (obj: any): FormField[] => {
  const fields: FormField[] = []
  const fieldMap = new Map<string, any>()
  
  extractAllFields(obj, '', fieldMap)
  
  fieldMap.forEach((value, path) => {
    fields.push({
      id: generateId(),
      name: path,
      type: inferFieldType(value),
      value: '', // Always start with empty value
      placeholder: Array.isArray(value) ? value.join(', ') : String(value || ''), // Use actual value as placeholder
      level: 0,
      required: false
    })
  })
  
  return fields
}

const extractAllFields = (obj: any, prefix: string, fieldMap: Map<string, any>) => {
  Object.entries(obj).forEach(([key, value]) => {
    const fieldPath = prefix ? `${prefix}.${key}` : key
    
    if (isPrimitiveValue(value)) {
      fieldMap.set(fieldPath, value)
    } else if (Array.isArray(value)) {
      if (value.length > 0 && typeof value[0] === 'object') {
        extractAllFields(value[0], fieldPath, fieldMap)
      } else {
        fieldMap.set(fieldPath, value)
      }
    } else if (typeof value === 'object' && value !== null) {
      extractAllFields(value, fieldPath, fieldMap)
    }
  })
}

const isPrimitiveValue = (value: any): boolean => {
  if (value === null || value === undefined) return true
  if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') return true
  if (Array.isArray(value) && value.every(item => typeof item !== 'object')) return true
  return false
}

const inferFieldType = (value: any): 'string' | 'number' | 'date' | 'url' | 'email' | 'textarea' | 'array' => {
  if (Array.isArray(value)) return 'array'
  
  const str = String(value)
  
  if (str.startsWith('http://') || str.startsWith('https://')) return 'url'
  
  if (str.includes('@') && str.includes('.')) return 'email'
  
  if (/^\d{4}-\d{2}-\d{2}/.test(str)) return 'date'
  
  if (!isNaN(Number(str)) && str !== '') return 'number'
  
  if (str.length > 100) return 'textarea'
  
  return 'string'
}

export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9)
}

export const createEmptyCategory = (name: string = 'new_category', level: number = 0): FormCategory => ({
  id: generateId(),
  name,
  level,
  children: [],
  isCollapsed: false
})

export const getPlaceholderForType = (fieldType: FieldType): string => {
  switch (fieldType) {
    case 'string': return 'sample text'
    case 'textarea': return 'long text content'
    case 'number': return '123'
    case 'date': return '2024-01-01'
    case 'timestamp': return '2024-01-01T12:00:00Z'
    case 'geolocation': return '40.7128, -74.0060'
    case 'url': return 'https://example.com'
    case 'email': return '<EMAIL>'
    case 'array': return '[]'
    default: return 'value'
  }
}

export const getFieldDefaults = (fieldType: FieldType) => {
  switch (fieldType) {
    case 'string': return { name: 'text_field', value: '', placeholder: getPlaceholderForType(fieldType) }
    case 'textarea': return { name: 'description', value: '', placeholder: getPlaceholderForType(fieldType) }
    case 'number': return { name: 'count', value: '', placeholder: getPlaceholderForType(fieldType) }
    case 'date': return { name: 'created_date', value: '', placeholder: getPlaceholderForType(fieldType) }
    case 'timestamp': return { name: 'timestamp', value: '', placeholder: getPlaceholderForType(fieldType) }
    case 'geolocation': return { name: 'location', value: '', placeholder: getPlaceholderForType(fieldType) }
    case 'url': return { name: 'website_url', value: '', placeholder: getPlaceholderForType(fieldType) }
    case 'email': return { name: 'email_address', value: '', placeholder: getPlaceholderForType(fieldType) }
    case 'array': return { name: 'items', value: '', placeholder: getPlaceholderForType(fieldType) }
    default: return { name: 'new_field', value: '', placeholder: '' }
  }
}

export const createEmptyField = (name: string = 'New Field', level: number = 0): FormField => ({
  id: generateId(),
  name,
  type: 'string',
  value: '',
  level,
  required: false
})

export const createEmptySchema = (): FormSchema => ({
  items: []
})

export const sanitizeFieldName = (name: string): string => {
  let sanitized = name.trim()
  
  sanitized = sanitized.replace(/[^a-zA-Z0-9_]/g, '_')
  
  sanitized = sanitized.replace(/_+/g, '_')
  
  sanitized = sanitized.replace(/^_+|_+$/g, '')
  
  if (/^\d/.test(sanitized)) {
    sanitized = `field_${sanitized}`
  }
  
  sanitized = sanitized.toLowerCase()
  
  if (!sanitized) {
    sanitized = 'field'
  }
  
  return sanitized
}

export const isValidJsonKey = (name: string): boolean => {
  if (!name || !name.trim()) return false
  
  if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name)) return false
  
  const reserved = ['constructor', 'prototype', '__proto__', 'hasOwnProperty']
  if (reserved.includes(name.toLowerCase())) return false
  
  return true
}


export interface FormLine {
  id: string
  item: FormItem
  lineNumber: number
  parentId?: string
  depth: number
  isLastInParent: boolean
}

export const flattenToLines = (items: FormItem[], parentId?: string, depth = 0): FormLine[] => {
  const lines: FormLine[] = []
  
  items.forEach((item, index) => {
    const isLastInParent = index === items.length - 1
    
    lines.push({
      id: item.id,
      item: { ...item },
      lineNumber: lines.length + 1,
      parentId,
      depth,
      isLastInParent
    })
    
    if ('children' in item && item.children && item.children.length > 0) {
      const childLines = flattenToLines(item.children, item.id, depth + 1)
      lines.push(...childLines)
    }
  })
  
  return lines
}

export const reconstructFromLines = (lines: FormLine[]): FormItem[] => {
  if (lines.length === 0) return []
  
  const result: FormItem[] = []
  const stack: Array<{ item: FormItem & { children: FormItem[] }, depth: number }> = []
  
  lines.forEach(line => {
    const { item, depth } = line
    
    const newItem: FormItem = {
      ...item,
      level: depth,
      ...(('children' in item) ? { children: [] } : {})
    }
    
    while (stack.length > 0 && stack[stack.length - 1].depth >= depth) {
      stack.pop()
    }
    
    if (depth === 0 || stack.length === 0) {
      result.push(newItem)
      if ('children' in newItem) {
        stack.push({ 
          item: newItem as FormItem & { children: FormItem[] }, 
          depth: depth 
        })
      }
    } else {
      const parent = stack[stack.length - 1]
      if (parent && 'children' in parent.item) {
        parent.item.children.push(newItem)
        if ('children' in newItem) {
          stack.push({ 
            item: newItem as FormItem & { children: FormItem[] }, 
            depth: depth 
          })
        }
      }
    }
  })
  
  return result
}

export const moveLineToPosition = (
  items: FormItem[], 
  sourceId: string, 
  targetId: string
): FormItem[] => {
  if (sourceId === targetId) return items
  
  const lines = flattenToLines(items)
  
  const sourceIndex = lines.findIndex(line => line.id === sourceId)
  if (sourceIndex === -1) return items
  
  const sourceLine = lines[sourceIndex]
  const movingLines: FormLine[] = [sourceLine]
  
  if ('children' in sourceLine.item && sourceLine.item.children && sourceLine.item.children.length > 0) {
    for (let i = sourceIndex + 1; i < lines.length; i++) {
      const line = lines[i]
      if (line.depth > sourceLine.depth) {
        movingLines.push(line)
      } else {
        break
      }
    }
  }
  
  const targetIndex = lines.findIndex(line => line.id === targetId)
  if (targetIndex === -1) return items
  
  const isMovingIntoSelf = movingLines.some(line => line.id === targetId)
  if (isMovingIntoSelf) return items
  
  const remainingLines = lines.filter(line => 
    !movingLines.some(movingLine => movingLine.id === line.id)
  )
  
  let insertIndex = remainingLines.findIndex(line => line.id === targetId)
  if (insertIndex === -1) insertIndex = remainingLines.length
  
  const reorderedLines = [
    ...remainingLines.slice(0, insertIndex),
    ...movingLines,
    ...remainingLines.slice(insertIndex)
  ]
  
  return reconstructFromLines(reorderedLines)
}

export interface DropZone {
  id: string
  lineNumber: number
  depth: number
  type: 'before' | 'inside' | 'after'
  targetItem: FormItem
}

export const getDropZones = (items: FormItem[]): DropZone[] => {
  const lines = flattenToLines(items)
  const dropZones: DropZone[] = []
  
  lines.forEach((line, index) => {
    dropZones.push({
      id: `before-${line.id}`,
      lineNumber: line.lineNumber,
      depth: line.depth,
      type: 'before',
      targetItem: line.item
    })
    
    if ('children' in line.item) {
      dropZones.push({
        id: `inside-${line.id}`,
        lineNumber: line.lineNumber,
        depth: line.depth + 1,
        type: 'inside',
        targetItem: line.item
      })
    }
    
    if (index === lines.length - 1 || lines[index + 1].depth <= line.depth) {
      dropZones.push({
        id: `after-${line.id}`,
        lineNumber: line.lineNumber + (line.isLastInParent ? 1 : 0),
        depth: line.depth,
        type: 'after',
        targetItem: line.item
      })
    }
  })
  
  return dropZones
}

export interface ValidationError {
  id: string
  field: string
  message: string
  type: 'error' | 'warning'
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationError[]
}

const validateFormItem = (item: FormItem, siblings: FormItem[], path: string = ''): ValidationError[] => {
  const errors: ValidationError[] = []
  const currentPath = path ? `${path}.${item.name}` : item.name

  if (!item.name || !item.name.trim()) {
    errors.push({
      id: item.id,
      field: currentPath,
      message: 'Field name cannot be empty',
      type: 'error'
    })
  }

  if (item.name && !isValidJsonKey(item.name)) {
    errors.push({
      id: item.id,
      field: currentPath,
      message: `Field name "${item.name}" contains invalid characters. Use only letters, numbers, and underscores.`,
      type: 'error'
    })
  }


  const duplicates = siblings.filter(sibling => 
    sibling.id !== item.id && sibling.name === item.name
  )
  if (duplicates.length > 0) {
    errors.push({
      id: item.id,
      field: currentPath,
      message: `Duplicate field name "${item.name}" found at this level. Field names must be unique.`,
      type: 'error'
    })
  }

  if ('type' in item) {
    const field = item as FormField
    
    const validTypes: FieldType[] = ['string', 'number', 'date', 'url', 'email', 'textarea', 'array', 'timestamp', 'geolocation']
    if (!validTypes.includes(field.type)) {
      errors.push({
        id: item.id,
        field: currentPath,
        message: `Invalid field type "${field.type}"`,
        type: 'error'
      })
    }

    if (!field.value || field.value.trim() === '') {
      errors.push({
        id: item.id,
        field: currentPath,
        message: `Field "${item.name}" is empty. Please enter data or remove the field.`,
        type: 'error'
      })
    }

    if (field.required && (!field.value || field.value.trim() === '')) {
      errors.push({
        id: item.id,
        field: currentPath,
        message: `Required field "${item.name}" is empty`,
        type: 'warning'
      })
    }
  }

  if ('children' in item) {
    const category = item as FormCategory
    
    if (!category.children || category.children.length === 0) {
      errors.push({
        id: item.id,
        field: currentPath,
        message: `Category "${item.name}" is empty. Add fields or remove the category.`,
        type: 'warning'
      })
    } else {
      category.children.forEach(child => {
        const childErrors = validateFormItem(child, category.children, currentPath)
        errors.push(...childErrors)
      })
    }
  }

  return errors
}

export const validateFormSchema = (schema: FormSchema): ValidationResult => {
  const errors: ValidationError[] = []
  
  if (!schema.items || schema.items.length === 0) {
    return {
      isValid: false,
      errors: [{
        id: 'schema',
        field: 'root',
        message: 'Form is empty. Add at least one field or category.',
        type: 'error'
      }],
      warnings: []
    }
  }

  schema.items.forEach(item => {
    const itemErrors = validateFormItem(item, schema.items)
    errors.push(...itemErrors)
  })

  const fieldCount = countAllFields(schema.items)
  if (fieldCount > 100) {
    errors.push({
      id: 'schema',
      field: 'root',
      message: `Too many fields (${fieldCount}/100). Remove some fields to continue.`,
      type: 'error'
    })
  }

  const errorList = errors.filter(e => e.type === 'error')
  const warningList = errors.filter(e => e.type === 'warning')

  return {
    isValid: errorList.length === 0,
    errors: errorList,
    warnings: warningList
  }
}

const countAllFields = (items: FormItem[]): number => {
  return items.reduce((count, item) => {
    if ('type' in item) {
      return count + 1
    } else if ('children' in item && item.children) {
      return count + countAllFields(item.children)
    }
    return count
  }, 0)
}

export const calculateJsonSize = (jsonData: JsonData): string => {
  try {
    const jsonString = JSON.stringify(jsonData, null, 2)
    const sizeInBytes = new Blob([jsonString]).size
    const sizeInKB = sizeInBytes / 1024
    
    if (sizeInKB < 1) {
      return `${sizeInBytes} bytes`
    } else if (sizeInKB < 100) {
      return `${sizeInKB.toFixed(1)} KB`
    } else {
      return `${Math.round(sizeInKB)} KB`
    }
  } catch (error) {
    return '0 KB'
  }
}