import React, { useState, useEffect, useMemo } from 'react'
import { But<PERSON> } from '@/lib/components/ui/button'
import { StepActions } from '@/lib/components/ui/step-actions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/lib/components/ui/card'
import { Alert, AlertDescription } from '@/lib/components/ui/alert'
import { Input } from '@/lib/components/ui/input'
import { Label } from '@/lib/components/ui/label'
import { Textarea } from '@/lib/components/ui/textarea'
import { Switch } from '@/lib/components/ui/switch'
import { ArrowLeft, CheckCircle, XCircle, AlertTriangle, Lock, Eye, EyeOff, Share, QrCode, CreditCard, Coins } from 'lucide-react'
import { codeToHtml } from 'shiki'
import { useNetworkStore } from '@/lib/store/network'
import { useWallet, useAssets, useLovelace } from '@meshsdk/react'
import { toast } from 'sonner'
import axios from 'axios'
import type { FormData } from '@/pages/create'
import { processWalletError } from '@/lib/utils/wallet-error-handler'

interface DataConfirmationProps {
  formData: FormData
  onConfirm: (isPublic?: boolean, paymentHash?: string) => void
  onBack: () => void
}

type ValidationStatus = 'valid' | 'warning' | 'error'

interface ValidationResult {
  status: ValidationStatus
  message: string
  details?: string[]
}

export const DataConfirmation: React.FC<DataConfirmationProps> = ({ 
  formData, 
  onConfirm, 
  onBack 
}) => {
  const [validation, setValidation] = useState<ValidationResult | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [highlightedJson, setHighlightedJson] = useState<string>('')
  const [isPublic, setIsPublic] = useState(true) // Default to public
  const [paymentMethod, setPaymentMethod] = useState<'ADA' | 'PALM'>('ADA')
  const [feeAmounts, setFeeAmounts] = useState<{adaFeeAmount: number, palmFeeAmount: number, totalAdaFeeAmount: number} | null>(null)
  
  const { selectedNetwork } = useNetworkStore()
  const { wallet } = useWallet()
  const assets = useAssets()
  const lovelace = useLovelace()
  const isMainnet = selectedNetwork === true

  const PALM_UNIT = "b7c5cd554f3e83c8aa0900a0c9053284a5348244d23d0406c28eaf4d50414c4d0a"

  const formatADA = (lovelaceAmount: string): string => {
    const ada = parseFloat(lovelaceAmount) / 1_000_000
    return ada.toFixed(2)
  }

  const formatPALM = (palmAmount: string): string => {
    const palm = parseFloat(palmAmount) / 1_000_000
    return palm.toFixed(2)
  }

  const getPalmBalance = (): string => {
    if (!assets) return '0'
    const palmAsset = assets.find(asset => asset.unit === PALM_UNIT)
    return palmAsset ? palmAsset.quantity : '0'
  }

  const getAdaBalance = (): string => {
    return lovelace || '0'
  }

  const hasInsufficientBalance = (): boolean => {
    if (!wallet || !feeAmounts) return false
    
    if (paymentMethod === 'ADA') {
      const adaBalance = parseFloat(getAdaBalance()) / 1_000_000
      const requiredAmount = feeAmounts.totalAdaFeeAmount
      return adaBalance < requiredAmount
    } else {
      const palmBalance = parseFloat(getPalmBalance()) / 1_000_000
      const requiredAmount = feeAmounts.palmFeeAmount
      return palmBalance < requiredAmount
    }
  }

  useEffect(() => {
    validateData()
    generateHighlightedJson()
  }, [formData])

  useEffect(() => {
    const fetchFeeAmounts = async () => {
      try {
        const response = await axios.get('/api/fees/amounts')
        if (response.data.success) {
          setFeeAmounts({
            adaFeeAmount: response.data.adaFeeAmount,
            palmFeeAmount: response.data.palmFeeAmount,
            totalAdaFeeAmount: response.data.totalAdaFeeAmount
          })
        }
      } catch (error) {
        setFeeAmounts({
          adaFeeAmount: 3.5,
          palmFeeAmount: 2000,
          totalAdaFeeAmount: 5.0
        })
      }
    }

    fetchFeeAmounts()
  }, [])

  const generateHighlightedJson = async () => {
    try {
      const html = await codeToHtml(JSON.stringify(formData, null, 2), {
        lang: 'json',
        theme: 'ayu-dark',
        colorReplacements: {
          '#0b0e14': '#2e2e2e',
        }
      })
      setHighlightedJson(html)
    } catch (error) {
      console.error('Failed to highlight JSON:', error)
      setHighlightedJson('')
    }
  }

  const validateData = () => {
    const errors: string[] = []
    const warnings: string[] = []

    Object.entries(formData).forEach(([key, value]) => {
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        errors.push(`${key} is empty`)
      }
    })

    Object.entries(formData).forEach(([key, value]) => {
      if (key.toLowerCase().includes('date') && value) {
        const date = new Date(value as string)
        if (isNaN(date.getTime())) {
          errors.push(`${key} has invalid date format`)
        }
      }
      
      if (key.toLowerCase().includes('id') && value) {
        if (typeof value === 'string' && value.length < 3) {
          warnings.push(`${key} seems too short for an ID`)
        }
      }
    })

    let status: ValidationStatus = 'valid'
    let message = 'Data is ready for submission'
    
    if (errors.length > 0) {
      status = 'error'
      message = `Found ${errors.length} error(s) that need to be fixed`
    } else if (warnings.length > 0) {
      status = 'warning'
      message = `Data is valid but has ${warnings.length} warning(s)`
    }

    setValidation({
      status,
      message,
      details: [...errors, ...warnings]
    })
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    
    try {
      if (isMainnet) {
        await handleMainnetPayment()
      } else {
        await new Promise(resolve => setTimeout(resolve, 1000))
        onConfirm(true)
      }
    } catch (error: any) {
      setIsSubmitting(false)
      
    }
  }

  const handleMainnetPayment = async () => {
    if (!wallet) {
      toast.error('Wallet not connected')
      throw new Error('Wallet not connected')
    }

    try {
      toast.info(`Preparing ${paymentMethod} payment...`)

      const utxos = await wallet.getUtxos()
      const changeAddress = await wallet.getChangeAddress()

      if (!utxos || utxos.length === 0) {
        toast.error('No UTXOs available in wallet')
        throw new Error('No UTXOs available')
      }

      toast.info('Building transaction...')
      const transactionResponse = await axios.post('/api/transaction/submit', {
        changeAddress,
        utxos,
        withPalm: paymentMethod === 'PALM'
      })

      if (!transactionResponse.data.success) {
        toast.error(transactionResponse.data.message || 'Failed to build transaction')
        throw new Error(transactionResponse.data.message)
      }

      const unsignedTx = transactionResponse.data.unsignedTx

      toast.info('Please sign the transaction in your wallet...')
      const signedTx = await wallet.signTx(unsignedTx)

      toast.info('Submitting payment transaction...')
      const paymentHash = await wallet.submitTx(signedTx)

      toast.success(`Payment successful! Transaction: ${paymentHash.substring(0, 10)}...`)

      setIsSubmitting(false)
      onConfirm(isPublic, paymentHash)

    } catch (error: any) {
      
      let errorMessage = 'Payment transaction failed'
      
      if (error.response) {
        const responseData = error.response.data
        if (responseData?.message) {
          errorMessage = responseData.message
        } else if (error.response.status === 400) {
          errorMessage = 'Invalid payment data. Please check your wallet and try again.'
        } else if (error.response.status >= 500) {
          errorMessage = 'Payment service is temporarily unavailable. Please try again.'
        }
      } else {
        const walletErrorInfo = processWalletError(error, 'Payment transaction')
        errorMessage = walletErrorInfo.message
        
        if (errorMessage.includes('Insufficient funds')) {
          errorMessage = `Insufficient ${paymentMethod} funds to complete payment`
        }
      }

      toast.error(errorMessage)
    }
  }

  const renderFormField = (key: string, value: any, depth: number = 0): React.ReactNode => {
    const fieldName = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
    const indentClass = depth > 0 ? `ml-${depth * 4}` : ''
    
    if (value === null || value === undefined) {
      return (
        <div key={key} className={`space-y-2 ${indentClass}`}>
          <Label className="text-sm font-medium text-muted-foreground">{fieldName}</Label>
          <Input 
            value="null" 
            disabled 
            className="bg-muted/50 text-muted-foreground font-mono"
          />
        </div>
      )
    }
    
    if (typeof value === 'object' && !Array.isArray(value)) {
      return (
        <div key={key} className={`space-y-3 ${indentClass}`}>
          <div className="flex items-center gap-2">
            <Label className="text-sm font-medium">{fieldName}</Label>
            <div className="h-px bg-border flex-1" />
          </div>
          <div className="space-y-3 pl-4 border-l-2 border-muted">
            {Object.entries(value).map(([nestedKey, nestedValue]) =>
              renderFormField(nestedKey, nestedValue, depth + 1)
            )}
          </div>
        </div>
      )
    }
    
    if (Array.isArray(value)) {
      const displayValue = JSON.stringify(value)
      const isLong = displayValue.length > 50
      
      return (
        <div key={key} className={`space-y-2 ${indentClass}`}>
          <Label className="text-sm font-medium text-muted-foreground">{fieldName}</Label>
          {isLong ? (
            <Textarea 
              value={displayValue}
              disabled 
              className="bg-muted/50 text-muted-foreground font-mono resize-none"
              rows={3}
            />
          ) : (
            <Input 
              value={displayValue}
              disabled 
              className="bg-muted/50 text-muted-foreground font-mono"
            />
          )}
        </div>
      )
    }
    
    if (typeof value === 'string') {
      const isLong = value.length > 50
      const displayValue = value.trim() === '' ? '(empty)' : value
      
      return (
        <div key={key} className={`space-y-2 ${indentClass}`}>
          <Label className="text-sm font-medium text-muted-foreground">{fieldName}</Label>
          {isLong ? (
            <Textarea 
              value={displayValue}
              disabled 
              className="bg-muted/50 text-muted-foreground font-mono resize-none"
              rows={3}
            />
          ) : (
            <Input 
              value={displayValue}
              disabled 
              className="bg-muted/50 text-muted-foreground font-mono"
            />
          )}
        </div>
      )
    }
    
    return (
      <div key={key} className={`space-y-2 ${indentClass}`}>
        <Label className="text-sm font-medium text-muted-foreground">{fieldName}</Label>
        <Input 
          value={value.toString()}
          disabled 
          className={`bg-muted/50 font-mono font-medium ${
            typeof value === 'boolean' 
              ? value ? 'text-emerald-600 dark:text-emerald-400' : 'text-red-600 dark:text-red-400'
              : typeof value === 'number'
              ? 'text-blue-600 dark:text-blue-400'
              : 'text-muted-foreground'
          }`}
        />
      </div>
    )
  }

  const getValidationIcon = () => {
    if (!validation) return null
    
    switch (validation.status) {
      case 'valid':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />
    }
  }

  const canSubmit = validation?.status !== 'error' && (!isMainnet || !hasInsufficientBalance())

  return (
    <div className="space-y-6 md:space-y-8">
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4">
        <Button variant="outline" onClick={onBack} className="self-start w-full sm:w-auto">
          ← Back
        </Button>
        <div className="text-center md:flex-1">
          <h1 className="text-2xl md:text-3xl font-bold mb-2 md:mb-4">Confirm Your Data</h1>
          <p className="text-muted-foreground text-base md:text-lg">
            Review your information before submitting to the blockchain
          </p>
        </div>
      </div>

      <div className="max-w-4xl mx-auto space-y-6">
        {validation && validation.status !== 'valid' && (
          <Alert className={`${
            validation.status === 'error' ? 'border-red-200 bg-red-50' :
            'border-yellow-200 bg-yellow-50'
          }`}>
            <div className="flex items-center space-x-2">
              {getValidationIcon()}
              <AlertDescription className="flex-1">
                <div className="font-medium">{validation.message}</div>
                {validation.details && validation.details.length > 0 && (
                  <ul className="mt-2 space-y-1">
                    {validation.details.map((detail, index) => (
                      <li key={index} className="text-sm">• {detail}</li>
                    ))}
                  </ul>
                )}
              </AlertDescription>
            </div>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Lock className="w-5 h-5 text-muted-foreground" />
              <div>
                <CardTitle>Data Preview</CardTitle>
                <CardDescription>
                  Review your data before submitting to the Cardano blockchain
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(formData).map(([key, value]) => 
                renderFormField(key, value)
              )}
            </div>
          </CardContent>
        </Card>

        {isMainnet && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {isPublic ? <Eye className="w-5 h-5" /> : <EyeOff className="w-5 h-5" />}
                <span>Privacy Settings</span>
              </CardTitle>
              <CardDescription>
                Choose whether this record should be publicly visible or private
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="public-toggle" className="text-base font-medium">
                    {isPublic ? 'This record is public' : 'This record is private'}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {isPublic 
                      ? 'Public records can be viewed by anyone and shared via QR codes'
                      : 'Private records are only visible to you when logged in'
                    }
                  </p>
                </div>
                <Switch
                  id="public-toggle"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                />
              </div>
              
              <div className="space-y-3 pt-2 border-t">
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-full ${isPublic ? 'bg-green-100 dark:bg-green-950/20' : 'bg-muted'}`}>
                    {isPublic ? (
                      <Share className="w-4 h-4 text-green-500 dark:text-green-400" />
                    ) : (
                      <Share className="w-4 h-4 text-muted-foreground" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Sharing & QR Codes</p>
                    <p className="text-xs text-muted-foreground">
                      {isPublic 
                        ? 'Generate QR codes and share links with partners' 
                        : 'QR codes and public sharing not available for private records'
                      }
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-full ${isPublic ? 'bg-green-100 dark:bg-green-950/20' : 'bg-muted'}`}>
                    {isPublic ? (
                      <Eye className="w-4 h-4 text-green-500 dark:text-green-400" />
                    ) : (
                      <EyeOff className="w-4 h-4 text-muted-foreground" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Visibility</p>
                    <p className="text-xs text-muted-foreground">
                      {isPublic 
                        ? 'Visible in public explorer and can be found by anyone' 
                        : 'Only visible to you when logged in with your wallet'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {isMainnet && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="w-5 h-5" />
                <span>Payment Method</span>
              </CardTitle>
              <CardDescription>
                Choose how you want to pay for creating this record on mainnet
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div 
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    paymentMethod === 'ADA' 
                      ? 'border-primary bg-primary/5' 
                      : 'border-muted hover:border-primary/50'
                  }`}
                  onClick={() => setPaymentMethod('ADA')}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full ${
                      paymentMethod === 'ADA' 
                        ? 'bg-primary/10 text-primary' 
                        : 'bg-muted text-muted-foreground'
                    }`}>
                      <Coins className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">Pay with ADA</p>
                      <p className="text-sm text-muted-foreground">
                        {feeAmounts ? `Fee: ${feeAmounts.totalAdaFeeAmount} ADA` : 'Standard Cardano native token'}
                      </p>
                      {wallet && (
                        <p className="text-xs font-mono text-blue-600 dark:text-blue-400 mt-1">
                          Balance: {formatADA(getAdaBalance())} ADA
                        </p>
                      )}
                    </div>
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      paymentMethod === 'ADA' 
                        ? 'border-primary bg-primary' 
                        : 'border-muted'
                    }`}>
                      {paymentMethod === 'ADA' && (
                        <div className="w-full h-full rounded-full bg-white scale-50" />
                      )}
                    </div>
                  </div>
                </div>

                <div 
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    paymentMethod === 'PALM' 
                      ? 'border-primary bg-primary/5' 
                      : 'border-muted hover:border-primary/50'
                  }`}
                  onClick={() => setPaymentMethod('PALM')}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full ${
                      paymentMethod === 'PALM' 
                        ? 'bg-primary/10 text-primary' 
                        : 'bg-muted text-muted-foreground'
                    }`}>
                      <Coins className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">Pay with PALM</p>
                      <p className="text-sm text-muted-foreground">
                        {feeAmounts ? `Fee: ${feeAmounts.palmFeeAmount} PALM` : 'Winter Protocol utility token'}
                      </p>
                      {wallet && (
                        <p className="text-xs font-mono text-purple-600 dark:text-purple-400 mt-1">
                          Balance: {formatPALM(getPalmBalance())} PALM
                        </p>
                      )}
                    </div>
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      paymentMethod === 'PALM' 
                        ? 'border-primary bg-primary' 
                        : 'border-muted'
                    }`}>
                      {paymentMethod === 'PALM' && (
                        <div className="w-full h-full rounded-full bg-white scale-50" />
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="pt-2 border-t space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-950/20">
                    <AlertTriangle className="w-4 h-4 text-blue-500 dark:text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Payment Required</p>
                    <p className="text-xs text-muted-foreground">
                      {paymentMethod === 'ADA' 
                        ? feeAmounts ? `${feeAmounts.totalAdaFeeAmount} ADA total fee required to create records on mainnet` : 'A small ADA fee is required to create records on mainnet'
                        : feeAmounts ? `${feeAmounts.palmFeeAmount} PALM tokens + network fees required for record creation` : 'PALM tokens provide discounted fees for record creation'
                      }
                    </p>
                  </div>
                </div>

                {wallet && hasInsufficientBalance() && (
                  <Alert className="border-red-200 bg-red-50 dark:bg-red-950/20">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <AlertDescription>
                      <strong>Insufficient Balance:</strong> You don&apos;t have enough {paymentMethod} to complete this transaction. 
                      Please add more funds to your wallet or choose a different payment method.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>JSON Schema</CardTitle>
            <CardDescription>
              Raw data structure that will be stored on the blockchain
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-lg overflow-auto max-h-64 md:max-h-96 border bg-muted">
              {highlightedJson ? (
                <div 
                  className="text-xs md:text-sm font-mono [&_pre]:p-4 [&_pre]:m-0 [&_pre]:bg-transparent [&_pre]:font-mono"
                  dangerouslySetInnerHTML={{ __html: highlightedJson }}
                />
              ) : (
                <pre className="bg-muted p-3 md:p-4 text-xs md:text-sm font-mono text-foreground">
                  {JSON.stringify(formData, null, 2)}
                </pre>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-center pt-6">
          <Button 
            size="lg" 
            onClick={handleSubmit}
            disabled={!canSubmit || isSubmitting}
            className="px-6 md:px-8 w-full md:w-auto"
          >
            {isSubmitting ? 'Processing...' : 
             isMainnet && hasInsufficientBalance() ? `Insufficient ${paymentMethod}` :
             isMainnet && feeAmounts ? `Pay ${paymentMethod === 'ADA' ? feeAmounts.totalAdaFeeAmount : feeAmounts.palmFeeAmount} ${paymentMethod}` :
             isMainnet ? `Confirm with ${paymentMethod}` : 'Confirm and Submit'}
          </Button>
        </div>
      </div>
    </div>
  )
}