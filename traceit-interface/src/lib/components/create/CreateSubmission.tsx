import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/lib/components/ui/card'
import { Alert, AlertDescription } from '@/lib/components/ui/alert'
import { Badge } from '@/lib/components/ui/badge'
import { Button } from '@/lib/components/ui/button'
import { Loader2, CheckCircle, AlertTriangle, ExternalLink } from 'lucide-react'
import { useNetworkStore } from '@/lib/store/network'
import { useWallet } from '@meshsdk/react'
import type { FormData } from '@/pages/create'
import axios from 'axios'
import { motion, AnimatePresence } from 'motion/react'
import { processWalletError } from '@/lib/utils/wallet-error-handler'

interface CreateSubmissionProps {
  formData: FormData
  onSuccess: (recordData: SubmissionResult) => void
  onBack: () => void
  isPublic?: boolean
  paymentHash?: string | null
}

interface SubmissionResult {
  winterId: string
  tokenName: string
  metadataReference: string
  txHash?: string
  recordId?: string
  status: string
}

export const CreateSubmission: React.FC<CreateSubmissionProps> = ({
  formData,
  onSuccess,
  onBack,
  isPublic = true,
  paymentHash = null
}) => {
  const [status, setStatus] = useState<'creating' | 'validating' | 'success' | 'error'>('creating')
  const [submissionResult, setSubmissionResult] = useState<SubmissionResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [pollCount, setPollCount] = useState(0)
  const [hasSubmitted, setHasSubmitted] = useState(false)
  
  const { selectedNetwork } = useNetworkStore()
  const { wallet } = useWallet()
  const isMainnet = selectedNetwork === true
  
  const globalSubmissionKey = `submission-${JSON.stringify(formData)}-${isMainnet}`

  useEffect(() => {
    if (hasSubmitted) {
      return
    }
    
    const globalLock = sessionStorage.getItem(globalSubmissionKey)
    if (globalLock) {
      setHasSubmitted(true)
      return
    }
    
    sessionStorage.setItem(globalSubmissionKey, Date.now().toString())
    setHasSubmitted(true)
    submitToBlockchain()
  }, [hasSubmitted, globalSubmissionKey])

  const submitToBlockchain = async () => {
    try {
      setStatus('creating')
      setError(null)

      // Get wallet address for mainnet
      let walletAddress = null
      if (isMainnet && wallet) {
        try {
          walletAddress = await wallet.getChangeAddress()
        } catch (error) {
              throw new Error('Failed to get wallet address')
        }
      }

      const createResponse = await axios.post('/api/tokenize/create', {
        jsonSchema: formData,
        walletAddress: walletAddress,
        isPublic: isPublic,
        isMainnet,
        paymentHash: paymentHash
      })

      if (!createResponse.data.success) {
        throw new Error(createResponse.data.message || 'Failed to create token')
      }

      const { id: winterId, tokenName, metadataReference, recordId } = createResponse.data
      
      setSubmissionResult({
        winterId,
        tokenName,
        metadataReference,
        recordId,
        status: 'PENDING'
      })

      setStatus('validating')
      startPolling(winterId)
      
      sessionStorage.removeItem(globalSubmissionKey)

    } catch (err: any) {
      setStatus('error')
      
      let errorMessage = 'Failed to submit record'
      
      if (err.response) {
        // Server responded with error status
        const statusCode = err.response.status
        const responseData = err.response.data
        
        // Use the server's message if available, otherwise provide user-friendly defaults
        if (responseData?.message) {
          errorMessage = responseData.message
        } else if (statusCode === 400) {
          errorMessage = 'Invalid data provided. Please check your information and try again.'
        } else if (statusCode === 401) {
          errorMessage = 'Authentication required. Please connect and verify your wallet.'
        } else if (statusCode === 403) {
          errorMessage = 'Access denied. Please check your permissions.'
        } else if (statusCode === 404) {
          errorMessage = 'Required service not found. Please try again later.'
        } else if (statusCode === 409) {
          errorMessage = 'Record already exists or conflict occurred. Please try again.'
        } else if (statusCode === 422) {
          errorMessage = 'Data validation failed. Please check your input.'
        } else if (statusCode === 429) {
          errorMessage = 'Too many requests. Please wait a moment before trying again.'
        } else if (statusCode >= 500) {
          errorMessage = 'Server error occurred. Please try again in a few moments.'
        } else {
          errorMessage = responseData?.error || `Request failed (${statusCode})`
        }
      } else if (err.request) {
        // Network error
        errorMessage = 'Network connection failed. Please check your internet and try again.'
      } else {
        // Use wallet error handler for other types of errors (including wallet errors)
        const walletErrorInfo = processWalletError(err, 'Record submission')
        errorMessage = walletErrorInfo.message
      }
      
      setError(errorMessage)
      
      sessionStorage.removeItem(globalSubmissionKey)
    }
  }

  const startPolling = (winterId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        setPollCount(prev => prev + 1)
        
        const validateResponse = await axios.post('/api/tokenize/validate', {
          winterId,
          isMainnet
        })


        setSubmissionResult(prev => prev ? {
          ...prev,
          status: validateResponse.data.status,
          ...(validateResponse.data.txHash && { txHash: validateResponse.data.txHash })
        } : null)

        if (validateResponse.data.status === 'SUCCESS' && 
            validateResponse.data.txHash && 
            validateResponse.data.txHash !== '' && 
            validateResponse.data.txHash !== 'No tx id.') {
          clearInterval(pollInterval)
          setStatus('success')
          
          setSubmissionResult(prev => {
            const finalResult: SubmissionResult = {
              winterId,
              tokenName: prev?.tokenName || '',
              metadataReference: validateResponse.data.metadataReference || prev?.metadataReference || '',
              txHash: validateResponse.data.txHash,
              recordId: prev?.recordId,
              status: validateResponse.data.status
            }
            
            setTimeout(() => {
              onSuccess(finalResult)
            }, 5000)
            
            return finalResult
          })
        } else if (validateResponse.data.status === 'FAILED' || validateResponse.data.status === 'ERROR') {
          clearInterval(pollInterval)
          setStatus('error')
          setError('Transaction failed. Please try creating a new record.')
        } else if (validateResponse.data.status === 'QUEUED' || 
                   validateResponse.data.status === 'PENDING' || 
                   validateResponse.data.status === 'PROCESSING') {
        }

        if (pollCount >= 12) {
          clearInterval(pollInterval)
          setStatus('error')
          setError('Transaction is taking longer than expected. The record may still be processing. Please check the blockchain explorer later.')
        }
      } catch (err: any) {
        
        if (pollCount >= 5) {
          clearInterval(pollInterval)
          setStatus('error')
          
          let errorMessage = 'Unable to verify transaction status'
          
          if (err.response?.data?.message) {
            errorMessage = err.response.data.message
          } else if (err.response?.status >= 500) {
            errorMessage = 'Verification service is temporarily unavailable. Your record may have been created successfully.'
          } else if (err.request) {
            errorMessage = 'Network connection failed during verification. Your record may have been created successfully.'
          } else {
            const walletErrorInfo = processWalletError(err, 'Transaction verification')
            errorMessage = `${walletErrorInfo.message}. Your record may have been created successfully.`
          }
          
          setError(errorMessage)
        }
      }
    }, 10000)
    return () => clearInterval(pollInterval)
  }

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6 md:space-y-8"
    >
      <motion.div 
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="text-center"
      >
        <h1 className="text-2xl md:text-3xl font-bold mb-2 md:mb-4">Submitting Your Record</h1>
        <p className="text-muted-foreground text-base md:text-lg">
          Creating your immutable record on the Cardano blockchain
        </p>
      </motion.div>

      <div className="max-w-2xl mx-auto space-y-6">
        <AnimatePresence>
          {!isMainnet && (
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Alert className="border-amber-200 bg-amber-50 dark:bg-amber-950/20">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
                <AlertDescription>
                  <strong>Preview Network Notice:</strong> This record will be permanently stored on the Cardano preview network. 
                  It will not be available for viewing on trace.it after creation. To create shareable records that can be 
                  accessed later, please switch to mainnet and pay the required fee.
                </AlertDescription>
              </Alert>
            </motion.div>
          )}
        </AnimatePresence>

        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Submission Status</CardTitle>
                <motion.div
                  key={status}
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <Badge variant={
                    status === 'success' ? 'default' :
                    status === 'error' ? 'destructive' :
                    'secondary'
                  }>
                    {status === 'creating' && 'Creating Token'}
                    {status === 'validating' && 'Submitting to Blockchain'}
                    {status === 'success' && 'Success'}
                    {status === 'error' && 'Error'}
                  </Badge>
                </motion.div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="flex items-center gap-3"
                >
                  <motion.div
                    animate={status === 'creating' ? { rotate: 360 } : {}}
                    transition={status === 'creating' ? { duration: 1, repeat: Infinity, ease: "linear" } : {}}
                  >
                    {status === 'creating' ? (
                      <Loader2 className="w-5 h-5 text-primary" />
                    ) : (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                      >
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      </motion.div>
                    )}
                  </motion.div>
                  <div className="flex-1">
                    <p className="font-medium">Creating IPFS record</p>
                    <p className="text-sm text-muted-foreground">Storing your data on IPFS</p>
                  </div>
                </motion.div>

                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  className="flex items-center gap-3"
                >
                  <motion.div
                    animate={status === 'validating' ? { rotate: 360 } : {}}
                    transition={status === 'validating' ? { duration: 1, repeat: Infinity, ease: "linear" } : {}}
                  >
                    {status === 'creating' ? (
                      <div className="w-5 h-5 rounded-full border-2 border-muted" />
                    ) : status === 'validating' ? (
                      <Loader2 className="w-5 h-5 text-primary" />
                    ) : status === 'success' ? (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                      >
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      </motion.div>
                    ) : (
                      <div className="w-5 h-5 rounded-full border-2 border-muted" />
                    )}
                  </motion.div>
                  <div className="flex-1">
                    <p className="font-medium">Submitting to blockchain</p>
                    <motion.p 
                      key={`${status}-${pollCount}`}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.3 }}
                      className="text-sm text-muted-foreground"
                    >
                      {status === 'validating' && (
                        submissionResult?.status === 'QUEUED' ? 'Transaction queued...' :
                        submissionResult?.status === 'PENDING' ? 'Transaction pending...' :
                        submissionResult?.status === 'PROCESSING' ? 'Processing on blockchain...' :
                        submissionResult?.status === 'SUBMITTED' ? 'Submitted to blockchain...' :
                        `Checking status... (${pollCount * 10}s)`
                      )}
                      {status !== 'validating' && 'Finalizing on Cardano'}
                    </motion.p>
                  </div>
                </motion.div>
              </div>

            <AnimatePresence>
              {submissionResult && (
                <motion.div 
                  initial={{ opacity: 0, y: 20, height: 0 }}
                  animate={{ opacity: 1, y: 0, height: 'auto' }}
                  exit={{ opacity: 0, y: -20, height: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="mt-6 space-y-3 pt-4 border-t overflow-hidden"
                >
                  {submissionResult.tokenName && (
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.3 }}
                    >
                      <p className="text-sm text-muted-foreground">Token Name</p>
                      <p className="font-mono text-sm">{submissionResult.tokenName}</p>
                    </motion.div>
                  )}
                  
                  {submissionResult.winterId && (
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.4 }}
                    >
                      <p className="text-sm text-muted-foreground">Winter ID</p>
                      <p className="font-mono text-xs break-all">{submissionResult.winterId}</p>
                    </motion.div>
                  )}

                  {submissionResult.txHash && (
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.5 }}
                    >
                      <p className="text-sm text-muted-foreground">Transaction Hash</p>
                      <div className="flex items-center gap-2">
                        <p className="font-mono text-xs break-all flex-1">{submissionResult.txHash}</p>
                        <motion.a
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                          href={`https://${isMainnet ? '' : 'preview.'}cardanoscan.io/transaction/${submissionResult.txHash}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary hover:underline"
                        >
                          <ExternalLink className="w-4 h-4" />
                        </motion.a>
                      </div>
                    </motion.div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>

            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -20, scale: 0.95 }}
                  transition={{ duration: 0.3 }}
                >
                  <Alert className="border-red-500 bg-red-900/20">
                    <AlertDescription className="text-white">{error}</AlertDescription>
                  </Alert>
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>
        </motion.div>

        <AnimatePresence>
          {status === 'error' && (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="flex justify-center gap-4"
            >
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button variant="outline" onClick={onBack}>
                  Go Back
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button onClick={submitToBlockchain}>
                  Try Again
                </Button>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  )
}