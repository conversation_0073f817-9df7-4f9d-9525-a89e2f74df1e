import React, { useState, useEffect } from 'react'
import { Button } from '@/lib/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/lib/components/ui/card'
import { CheckCircle, ExternalLink, Copy, Plus, Zap, Shield, History, QrCode, Eye, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { useNetworkStore } from '@/lib/store/network'
import { Alert, AlertDescription } from '@/lib/components/ui/alert'
import { AlertTriangle } from 'lucide-react'
import { toast } from 'sonner'
import { motion, AnimatePresence } from 'motion/react'
import { QRCodeShare } from '@/lib/components/QRCodeShare'
import QRCode from 'qrcode'

interface CreateSuccessProps {
  onCreateAnother: () => void
  submissionResult?: {
    winterId: string
    tokenName: string
    metadataReference: string
    txHash?: string
    recordId?: string
    status: string
  }
}

export const CreateSuccess: React.FC<CreateSuccessProps> = ({ onCreateAnother, submissionResult }) => {
  const { selectedNetwork } = useNetworkStore()
  const isMainnet = selectedNetwork === true
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('')
  const [qrLoading, setQrLoading] = useState(true)
  
  const transactionHash = submissionResult?.txHash
  const winterId = submissionResult?.winterId
  const tokenName = submissionResult?.tokenName
  const metadataReference = submissionResult?.metadataReference
  const ipfsHash = metadataReference ? metadataReference.replace('ipfs://', '') : undefined
  const timestamp = new Date().toLocaleString()
  const recordId = submissionResult?.recordId
  
  
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
  const recordUrl = recordId ? `${baseUrl}/records/${recordId}` : ''

  // Generate QR code for mainnet records
  useEffect(() => {
    if (isMainnet && recordId) {
      generateQRCode()
    }
  }, [isMainnet, recordId])

  const generateQRCode = async () => {
    if (!recordId) return
    
    try {
      setQrLoading(true)
      const qrDataUrl = await QRCode.toDataURL(recordUrl, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M'
      })
      setQrCodeDataUrl(qrDataUrl)
    } catch (error) {
    } finally {
      setQrLoading(false)
    }
  }

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success(`${label} copied to clipboard`)
    } catch (error) {
      toast.error('Failed to copy to clipboard')
    }
  }

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
      className="space-y-6 md:space-y-8"
    >
      <motion.div 
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.7, delay: 0.2 }}
        className="text-center space-y-3 md:space-y-4"
      >
        <motion.div 
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ 
            duration: 0.8, 
            delay: 0.4,
            type: "spring",
            stiffness: 200,
            damping: 20
          }}
          className="flex justify-center"
        >
          <div className="p-3 md:p-4 bg-green-500/10 dark:bg-green-500/20 rounded-full">
            <CheckCircle className="w-10 h-10 md:w-12 md:h-12 text-green-500 dark:text-green-400" />
          </div>
        </motion.div>
        
        <motion.h1 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-2xl md:text-3xl font-bold px-4"
        >
          Congratulations!
        </motion.h1>
        
        <motion.p 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="text-muted-foreground text-base md:text-lg max-w-2xl mx-auto px-4"
        >
          Your traceability record has been successfully created and is now permanently stored on the Cardano {isMainnet ? '' : 'preview '}blockchain.
        </motion.p>
        
      </motion.div>

      <div className="max-w-2xl mx-auto space-y-4 md:space-y-6 px-4">
        <AnimatePresence>
          {!isMainnet && (
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.5, delay: 1.1 }}
            >
              <Alert className="border-amber-200 bg-amber-50 dark:bg-amber-950/20">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
                <AlertDescription>
                  <strong>Important:</strong> This record was created on the preview network and is not stored in our database. 
                  Save the transaction details below as you won&apos;t be able to view this record on trace.it later.
                </AlertDescription>
              </Alert>
            </motion.div>
          )}
        </AnimatePresence>

        <motion.div
          initial={{ opacity: 0, y: 30, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.6, delay: isMainnet ? 1.1 : 1.2 }}
        >
          <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-base md:text-lg">
              <span>Transaction Details</span>
              <ExternalLink className="w-4 h-4" />
            </CardTitle>
            <CardDescription className="text-sm">
              Your record is now immutable and publicly verifiable
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              {tokenName && (
                <div className="flex flex-col space-y-1 md:flex-row md:justify-between md:items-center md:space-y-0">
                  <span className="font-medium text-sm md:text-base">Token Name:</span>
                  <span className="font-mono text-xs md:text-sm">{tokenName}</span>
                </div>
              )}
              
              {winterId && (
                <div className="flex flex-col space-y-1 md:flex-row md:justify-between md:items-center md:space-y-0">
                  <span className="font-medium text-sm md:text-base">Winter ID:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-mono text-xs md:text-sm text-muted-foreground break-all">
                      {winterId.slice(0, 8)}...{winterId.slice(-6)}
                    </span>
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(winterId, 'Winter ID')}
                        className="shrink-0"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </motion.div>
                  </div>
                </div>
              )}
              
              {transactionHash && (
                <div className="flex flex-col space-y-1 md:flex-row md:justify-between md:items-center md:space-y-0">
                  <span className="font-medium text-sm md:text-base">Transaction Hash:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-mono text-xs md:text-sm text-muted-foreground break-all">
                      {transactionHash.slice(0, 10)}...{transactionHash.slice(-8)}
                    </span>
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(transactionHash, 'Transaction Hash')}
                        className="shrink-0"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </motion.div>
                  </div>
                </div>
              )}
              
              {ipfsHash && (
                <div className="flex flex-col space-y-1 md:flex-row md:justify-between md:items-center md:space-y-0">
                  <span className="font-medium text-sm md:text-base">IPFS Hash:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-mono text-xs md:text-sm text-muted-foreground break-all">
                      {ipfsHash.length > 20 ? `${ipfsHash.slice(0, 10)}...${ipfsHash.slice(-8)}` : ipfsHash}
                    </span>
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(ipfsHash, 'IPFS Hash')}
                        className="shrink-0"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </motion.div>
                  </div>
                </div>
              )}
              
              <div className="flex flex-col space-y-1 md:flex-row md:justify-between md:items-center md:space-y-0">
                <span className="font-medium text-sm md:text-base">Timestamp:</span>
                <span className="font-mono text-xs md:text-sm">{timestamp}</span>
              </div>
            </div>
            
            <div className="pt-4 border-t space-y-2">
              {transactionHash && (
                <Button variant="outline" className="w-full" asChild>
                  <a 
                    href={`https://${isMainnet ? '' : 'preview.'}cardanoscan.io/transaction/${transactionHash}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    View on Cardano Explorer
                    <ExternalLink className="w-4 h-4 ml-2" />
                  </a>
                </Button>
              )}
              
              {ipfsHash && (
                <Button variant="outline" className="w-full" asChild>
                  <a 
                    href={`https://ipfs.io/ipfs/${ipfsHash}`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    View on IPFS
                    <ExternalLink className="w-4 h-4 ml-2" />
                  </a>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
        </motion.div>

        <AnimatePresence>
          {isMainnet && recordId && (
            <motion.div
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.3 }}
            >
              <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950/20">
                <CardHeader className="text-center">
                  <CardTitle className="text-base md:text-lg flex items-center justify-center gap-2">
                    <QrCode className="w-5 h-5 text-blue-600" />
                    Share Your Record
                  </CardTitle>
                  <CardDescription className="text-sm">
                    Scan the QR code or use the buttons below to access your record
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex justify-center">
                    {qrLoading ? (
                      <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-300 rounded-lg">
                        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                      </div>
                    ) : qrCodeDataUrl ? (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5 }}
                        className="p-4 bg-white rounded-lg shadow-sm border"
                      >
                        <img 
                          src={qrCodeDataUrl} 
                          alt="QR Code for record"
                          className="w-64 h-64"
                        />
                      </motion.div>
                    ) : (
                      <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-300 rounded-lg">
                        <QrCode className="w-12 h-12 text-gray-400" />
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3">
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="flex-1">
                      <Button variant="default" className="w-full" asChild>
                        <Link href={`/records/${recordId}`}>
                          <Eye className="w-4 h-4 mr-2" />
                          Open Record Page
                        </Link>
                      </Button>
                    </motion.div>
                    
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className="flex-1">
                      <Button 
                        variant="outline" 
                        className="w-full"
                        onClick={() => window.open(`/records/${recordId}/qr`, '_blank')}
                      >
                        <QrCode className="w-4 h-4 mr-2" />
                        QR Code Page
                      </Button>
                    </motion.div>
                  </div>
                  
                  <div className="text-xs text-muted-foreground text-center pt-2 border-t">
                    Your record is now permanently stored and accessible via trace.it
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {!isMainnet && (
            <motion.div
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.6, delay: 1.4 }}
            >
              <Card className="border-primary/50 bg-primary/5">
            <CardHeader>
              <CardTitle className="text-base md:text-lg flex items-center gap-2">
                <Zap className="w-5 h-5 text-primary" />
                Submit on Mainnet
              </CardTitle>
              <CardDescription className="text-sm">
                Unlock the full potential of trace.it on mainnet
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <Shield className="w-5 h-5 text-primary mt-0.5" />
                  <div className="min-w-0">
                    <p className="font-medium text-sm">Permanent Storage</p>
                    <p className="text-xs text-muted-foreground">
                      Records are stored in our database and always accessible
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <History className="w-5 h-5 text-primary mt-0.5" />
                  <div className="min-w-0">
                    <p className="font-medium text-sm">View All Records</p>
                    <p className="text-xs text-muted-foreground">
                      Access your complete record history anytime
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <ExternalLink className="w-5 h-5 text-primary mt-0.5" />
                  <div className="min-w-0">
                    <p className="font-medium text-sm">Share & Collaborate</p>
                    <p className="text-xs text-muted-foreground">
                      Generate QR codes and share records with partners
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="pt-2">
                <p className="text-xs text-muted-foreground mb-3">
                  Switch to mainnet in the navigation bar to start creating permanent records
                </p>
              </div>
            </CardContent>
          </Card>
            </motion.div>
          )}
        </AnimatePresence>

        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: isMainnet ? 1.3 : 1.6 }}
          className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center pt-2"
        >
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button size="lg" onClick={onCreateAnother} className="px-6 md:px-8 w-full sm:w-auto">
              <Plus className="w-4 h-4 mr-2" />
              Create Another Record
            </Button>
          </motion.div>
          
          <AnimatePresence>
            {isMainnet && (
              <motion.div 
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                whileHover={{ scale: 1.02 }} 
                whileTap={{ scale: 0.98 }}
              >
                <Button variant="outline" size="lg" asChild className="px-6 md:px-8 w-full sm:w-auto">
                  <Link href="/records">
                    View All Records
                  </Link>
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    </motion.div>
  )
}