import React, { useState } from 'react'
import { Button } from '@/lib/components/ui/button'
import { Input } from '@/lib/components/ui/input'
import { Label } from '@/lib/components/ui/label'
import type { FormData } from '@/pages/create'

interface FormBuilderSimpleProps {
  templateId: string | null
  onSubmit: (data: FormData) => void
  onBack: () => void
}

export const FormBuilderSimple: React.FC<FormBuilderSimpleProps> = ({ templateId, onSubmit, onBack }) => {
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')

  const handleSubmit = () => {
    const formData: FormData = {
      title,
      description,
      type: 'custom',
      timestamp: new Date().toISOString()
    }
    onSubmit(formData)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold mb-2">Build Your Form</h2>
        <p className="text-muted-foreground">Create your custom traceability record</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label htmlFor="title">Record Title</Label>
          <Input
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter record title..."
          />
        </div>

        <div>
          <Label htmlFor="description">Description</Label>
          <Input
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Describe your record..."
          />
        </div>

        <div className="p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">JSON Preview:</h4>
          <pre className="text-sm">
{JSON.stringify({
  title: title || 'Record Title',
  description: description || 'Record Description',
  type: 'custom',
  timestamp: new Date().toISOString()
}, null, 2)}
          </pre>
        </div>
      </div>

      <div className="flex justify-between pt-4 border-t">
        <Button variant="outline" onClick={onBack}>
          ← Back
        </Button>
        <Button onClick={handleSubmit} disabled={!title || !description}>
          Continue to Review
        </Button>
      </div>
    </div>
  )
}