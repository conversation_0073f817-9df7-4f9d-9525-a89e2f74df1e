import React from 'react'

interface FormBuilderTestProps {
  templateId: string | null
  onSubmit: (data: any) => void
  onBack: () => void
}

export const FormBuilderTest: React.FC<FormBuilderTestProps> = ({ templateId, onSubmit, onBack }) => {
  return (
    <div>
      <h2>FormBuilder Test</h2>
      <p>This is a minimal test component</p>
      <button onClick={onBack}>Back</button>
      <button onClick={() => onSubmit({})}>Submit</button>
    </div>
  )
}