import React, { useState } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { Button } from '@/lib/components/ui/button'
import { StepActions } from '@/lib/components/ui/step-actions'
import type { DataSource } from '@/pages/create'

interface DataSourceSelectionProps {
  onSelect: (source: DataSource) => void
  step?: number
}

export const DataSourceSelection: React.FC<DataSourceSelectionProps> = ({ onSelect, step = 1 }) => {
  const [selectedView, setSelectedView] = useState<DataSource>(null)
  const [isCollapsing, setIsCollapsing] = useState(false)
  const [collapsingButton, setCollapsingButton] = useState<DataSource>(null)

  const handleOwnClick = () => {
    setSelectedView('own')
    setTimeout(() => {
      onSelect('own')
    }, 500)
  }

  const handleTemplateClick = () => {
    onSelect('template')
  }

  const handleBack = () => {
    setIsCollapsing(true)
    setCollapsingButton(selectedView)
    setTimeout(() => {
      setSelectedView(null)
      setTimeout(() => {
        setIsCollapsing(false)
        setCollapsingButton(null)
      }, 405)
    }, 100)
  }

  const getTitle = () => {
    if (!selectedView) {
      return "What data you want to trace?"
    }
    if (selectedView === 'own') {
      return "Creating with your own data format"
    }
    if (selectedView === 'template') {
      return "Creating from template"
    }
    return ""
  }

  return (
    <div className="space-y-6 md:space-y-8">
      <div className="text-center px-4">
        <h1 className="text-xl md:text-2xl font-bold mb-3 md:mb-4">{getTitle()}</h1>
      </div>

      <div className="max-w-6xl mx-auto min-h-[400px] md:min-h-[600px] relative">
        <AnimatePresence mode="wait">
          {!selectedView ? (
            <motion.div
              key="buttons"
              layoutId="container"
              className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 max-w-2xl mx-auto items-center justify-center text-center"
              initial={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button 
                  size="lg"
                  variant="selection"
                  onClick={handleOwnClick}
                >
                  <AnimatePresence>
                    {collapsingButton !== 'own' && (
                      <motion.span
                        initial={{ opacity: 0 }}
                        exit={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                      >
                        My Own Data
                      </motion.span>
                    )}
                  </AnimatePresence>
                </Button>
              </motion.div>
              
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button 
                  size="lg"
                  variant="selection"
                  onClick={handleTemplateClick}
                >
                  From Template
                </Button>
              </motion.div>
            </motion.div>
          ) : (
            <motion.div
              key="expanded"
              className="w-full min-h-[400px] md:h-[600px] bg-card border border-border rounded-lg shadow-lg flex flex-col"
              initial={{ 
                scale: 0.1,
                x: selectedView === 'own' ? -200 : 200,
                y: -250,
                opacity: 0
              }}
              animate={{ 
                scale: 1,
                x: 0,
                y: 0,
                opacity: 1
              }}
              exit={{
                scale: 0.1,
                x: selectedView === 'own' ? -200 : 200,
                y: -250,
                opacity: 0
              }}
              transition={{ 
                type: "spring", 
                stiffness: 300, 
                damping: 30,
                duration: 0.5
              }}
            >
              <AnimatePresence>
                {!isCollapsing && (
                  <motion.div
                    className="flex-1"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0 }}
                    transition={{ delay: isCollapsing ? 0 : 0.3, duration: 0.2 }}
                  >
                    {selectedView === 'own' && (
                      <div className="w-full h-full flex flex-col p-4 md:p-6">
                        <div className="text-center mb-6 md:mb-8">
                          <h2 className="text-lg md:text-xl font-semibold mb-3 md:mb-4">Form Configuration</h2>
                          <p className="text-muted-foreground text-sm md:text-base">Define your custom data collection form</p>
                        </div>
                        
                        <div className="flex-1 flex items-center justify-center">
                          <div className="space-y-4 md:space-y-6 w-full max-w-md">
                            <div className="space-y-2">
                              <label className="text-sm font-medium">Form Title</label>
                              <input 
                                type="text"
                                className="w-full p-3 border border-border rounded-lg text-sm md:text-base"
                                placeholder="Enter form title..."
                              />
                            </div>
                            
                            <div className="space-y-2">
                              <label className="text-sm font-medium">Description</label>
                              <textarea 
                                className="w-full p-3 border border-border rounded-lg h-20 md:h-24 text-sm md:text-base resize-none"
                                placeholder="Describe what this form will track..."
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                  </motion.div>
                )}
              </AnimatePresence>
              
              {!isCollapsing && selectedView === 'template' && (
                <motion.div 
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ delay: 0.4, duration: 0.2 }}
                >
                  <StepActions
                    onBack={handleBack}
                    onContinue={() => onSelect(selectedView)}
                  />
                </motion.div>
              )}
              {!isCollapsing && selectedView === 'own' && (
                <motion.div 
                  className="border-t px-4 md:px-6 py-3 md:py-4 flex justify-start"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ delay: 0.4, duration: 0.2 }}
                >
                  <div className="text-xs md:text-sm text-muted-foreground animate-pulse">
                    Loading form builder...
                  </div>
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}