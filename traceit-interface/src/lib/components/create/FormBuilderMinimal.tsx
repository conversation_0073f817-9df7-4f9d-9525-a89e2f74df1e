import React, { useState } from 'react'

interface FormBuilderMinimalProps {
  templateId: string | null
  onSubmit: (data: any) => void
  onBack: () => void
}

export const FormBuilderMinimal: React.FC<FormBuilderMinimalProps> = ({ templateId, onSubmit, onBack }) => {
  const [title, setTitle] = useState('')

  return (
    <div style={{ padding: '20px' }}>
      <h2>Build Your Form</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <label>Title:</label>
        <input 
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          style={{ 
            width: '100%', 
            padding: '8px', 
            border: '1px solid #ccc',
            marginTop: '5px'
          }}
        />
      </div>

      <div style={{ display: 'flex', justifyContent: 'space-between', paddingTop: '20px' }}>
        <button onClick={onBack} style={{ padding: '10px 20px' }}>
          Back
        </button>
        <button onClick={() => onSubmit({ title })} style={{ padding: '10px 20px' }}>
          Continue
        </button>
      </div>
    </div>
  )
}