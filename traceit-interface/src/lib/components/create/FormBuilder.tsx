import React, { useState } from 'react'
import { But<PERSON> } from '@/lib/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/lib/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/lib/components/ui/tabs'
import { Input } from '@/lib/components/ui/input'
import { Label } from '@/lib/components/ui/label'
import { ArrowLeft, Plus, Trash2, Code } from 'lucide-react'
import type { FormData } from '@/pages/create'

interface FormBuilderProps {
  templateId: string | null
  onSubmit: (data: FormData) => void
  onBack: () => void
}

interface FormField {
  id: string
  name: string
  type: 'text' | 'number' | 'date' | 'textarea'
  required: boolean
  value: string
}

export const FormBuilder: React.FC<FormBuilderProps> = ({ templateId, onSubmit, onBack }) => {
  const [fields, setFields] = useState<FormField[]>(() => {
    if (templateId === 'supply-chain') {
      return [
        { id: '1', name: 'Product ID', type: 'text', required: true, value: '' },
        { id: '2', name: 'Origin', type: 'text', required: true, value: '' },
        { id: '3', name: 'Destination', type: 'text', required: true, value: '' },
        { id: '4', name: 'Timestamp', type: 'date', required: true, value: '' }
      ]
    }
    return [
      { id: '1', name: 'Title', type: 'text', required: true, value: '' }
    ]
  })

  const addField = () => {
    const newField: FormField = {
      id: Date.now().toString(),
      name: 'New Field',
      type: 'text',
      required: false,
      value: ''
    }
    setFields([...fields, newField])
  }

  const removeField = (id: string) => {
    setFields(fields.filter(field => field.id !== id))
  }

  const updateField = (id: string, updates: Partial<FormField>) => {
    setFields(fields.map(field => 
      field.id === id ? { ...field, ...updates } : field
    ))
  }

  const generateJSON = () => {
    return JSON.stringify(
      fields.reduce((acc, field) => {
        acc[field.name] = field.value || null
        return acc
      }, {} as Record<string, any>),
      null,
      2
    )
  }

  const handleSubmit = () => {
    const formData = fields.reduce((acc, field) => {
      acc[field.name] = field.value
      return acc
    }, {} as FormData)
    
    onSubmit(formData)
  }

  const isFormValid = fields.every(field => !field.required || field.value.trim())

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <div className="text-center flex-1">
          <h1 className="text-3xl font-bold mb-4">Build Your Form</h1>
          <p className="text-muted-foreground text-lg">
            {templateId ? 'Customize your template' : 'Create your custom fields'}
          </p>
        </div>
      </div>

      <div className="max-w-6xl mx-auto">
        <Tabs defaultValue="form" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="form">Form Builder</TabsTrigger>
            <TabsTrigger value="json">
              <Code className="w-4 h-4 mr-2" />
              JSON Preview
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="form" className="space-y-6">
            <div className="space-y-4">
              {fields.map((field) => (
                <Card key={field.id}>
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center space-x-4">
                          <div className="flex-1">
                            <Label htmlFor={`name-${field.id}`}>Field Name</Label>
                            <Input
                              id={`name-${field.id}`}
                              value={field.name}
                              onChange={(e) => updateField(field.id, { name: e.target.value })}
                            />
                          </div>
                          <div>
                            <Label htmlFor={`type-${field.id}`}>Type</Label>
                            <select
                              id={`type-${field.id}`}
                              value={field.type}
                              onChange={(e) => updateField(field.id, { type: e.target.value as FormField['type'] })}
                              className="w-full px-3 py-2 border rounded-md"
                            >
                              <option value="text">Text</option>
                              <option value="number">Number</option>
                              <option value="date">Date</option>
                              <option value="textarea">Textarea</option>
                            </select>
                          </div>
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={`required-${field.id}`}
                              checked={field.required}
                              onChange={(e) => updateField(field.id, { required: e.target.checked })}
                            />
                            <Label htmlFor={`required-${field.id}`}>Required</Label>
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeField(field.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Label htmlFor={`value-${field.id}`}>Value</Label>
                    {field.type === 'textarea' ? (
                      <textarea
                        id={`value-${field.id}`}
                        value={field.value}
                        onChange={(e) => updateField(field.id, { value: e.target.value })}
                        className="w-full px-3 py-2 border rounded-md"
                        rows={3}
                        required={field.required}
                      />
                    ) : (
                      <Input
                        id={`value-${field.id}`}
                        type={field.type}
                        value={field.value}
                        onChange={(e) => updateField(field.id, { value: e.target.value })}
                        required={field.required}
                      />
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="flex justify-center">
              <Button variant="outline" onClick={addField}>
                <Plus className="w-4 h-4 mr-2" />
                Add Field
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="json">
            <Card>
              <CardHeader>
                <CardTitle>JSON Output</CardTitle>
                <CardDescription>
                  This is how your data will be structured on the blockchain
                </CardDescription>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto">
                  {generateJSON()}
                </pre>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-center pt-6">
          <Button 
            size="lg" 
            onClick={handleSubmit} 
            disabled={!isFormValid}
            className="px-8"
          >
            Continue to Review
          </Button>
        </div>
      </div>
    </div>
  )
}