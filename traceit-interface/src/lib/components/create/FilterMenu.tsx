import React, { useState } from 'react'
import { Button } from '@/lib/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/lib/components/ui/card'
import { Popover, PopoverContent, PopoverTrigger } from '@/lib/components/ui/popover'
import { Badge } from '@/lib/components/ui/badge'
import { Check, Filter, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FilterMenuProps {
  availableTags: string[]
  selectedTags: string[]
  onTagsChange: (tags: string[]) => void
}

export const FilterMenu: React.FC<FilterMenuProps> = ({
  availableTags,
  selectedTags,
  onTagsChange
}) => {
  const [isOpen, setIsOpen] = useState(false)

  const toggleTag = (tag: string) => {
    if (selectedTags.includes(tag)) {
      onTagsChange(selectedTags.filter(t => t !== tag))
    } else {
      onTagsChange([...selectedTags, tag])
    }
  }

  const clearAllFilters = () => {
    onTagsChange([])
  }

  const hasActiveFilters = selectedTags.length > 0

  return (
    <div className="flex items-center gap-2">
      {/* Active filter badges */}
      {hasActiveFilters && (
        <div className="flex items-center gap-1">
          {selectedTags.map((tag) => (
            <Badge
              key={tag}
              variant="secondary"
              className="text-xs capitalize cursor-pointer hover:bg-destructive/10 hover:text-destructive transition-colors"
              onClick={() => toggleTag(tag)}
            >
              {tag}
              <X className="w-3 h-3 ml-1" />
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-xs h-6 px-2 text-muted-foreground hover:text-destructive"
          >
            Clear all
          </Button>
        </div>
      )}

      {/* Filter menu popover */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            className={cn(
              "relative",
              hasActiveFilters && "border-primary/50 bg-primary/5"
            )}
          >
            <Filter className="w-4 h-4 mr-2" />
            Filters
            {hasActiveFilters && (
              <Badge 
                variant="default" 
                className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
              >
                {selectedTags.length}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64" align="end">
          <Card className="border-0 shadow-none">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">Filter by Tags</CardTitle>
                {hasActiveFilters && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAllFilters}
                    className="text-xs h-6 px-2"
                  >
                    Clear all
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                {availableTags.map((tag) => {
                  const isSelected = selectedTags.includes(tag)
                  return (
                    <div
                      key={tag}
                      className={cn(
                        "flex items-center justify-between p-2 rounded-md cursor-pointer transition-colors hover:bg-muted/50",
                        isSelected && "bg-primary/10 border border-primary/20"
                      )}
                      onClick={() => toggleTag(tag)}
                    >
                      <span className="text-sm capitalize font-medium">
                        {tag}
                      </span>
                      {isSelected && (
                        <Check className="w-4 h-4 text-primary" />
                      )}
                    </div>
                  )
                })}
              </div>
              
              {availableTags.length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No tags available
                </p>
              )}
            </CardContent>
          </Card>
        </PopoverContent>
      </Popover>
    </div>
  )
}