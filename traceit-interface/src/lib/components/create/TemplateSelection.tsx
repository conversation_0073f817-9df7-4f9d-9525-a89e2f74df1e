import React, { useState, useMemo } from 'react'
import { motion } from 'motion/react'
import { Button } from '@/lib/components/ui/button'
import { Input } from '@/lib/components/ui/input'
import { StepActions } from '@/lib/components/ui/step-actions'
import { TEMPLATES } from '@/lib/templates'
import { FilterMenu } from './FilterMenu'

interface TemplateSelectionProps {
  onSelect: (templateId: string, templateName: string) => void
  onBack: () => void
}

const templates = Object.entries(TEMPLATES).map(([id, template]) => ({
  id,
  title: template.name,
  description: template.description,
  tags: template.tags
}))

export const TemplateSelection: React.FC<TemplateSelectionProps> = ({ onSelect, onBack }) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  const availableTags = useMemo(() => {
    const allTags = templates.flatMap(template => template.tags)
    return [...new Set(allTags)].sort()
  }, [])

  const filteredTemplates = useMemo(() => {
    return templates.filter(template => {
      const matchesSearch = template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      
      const matchesTags = selectedTags.length === 0 || 
        selectedTags.some(selectedTag => template.tags.includes(selectedTag))
      
      return matchesSearch && matchesTags
    })
  }, [searchQuery, selectedTags])

  const handleContinue = () => {
    if (selectedTemplate) {
      const template = templates.find(t => t.id === selectedTemplate)
      onSelect(selectedTemplate, template?.title || '')
    }
  }

  return (
    <div className="space-y-6 md:space-y-8">
      <div className="max-w-6xl mx-auto min-h-[500px] md:min-h-[600px] relative">
        <motion.div
          initial={{ 
            scale: 0.1,
            x: 200,
            y: -250,
            opacity: 0
          }}
          animate={{ 
            scale: 1,
            x: 0,
            y: 0,
            opacity: 1
          }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 30,
            duration: 0.5
          }}
          className="w-full min-h-[500px] md:h-[600px] bg-card border border-border rounded-lg shadow-lg flex flex-col"
        >
        <div className="w-full h-full flex flex-col p-4 md:p-6">
          <div className="flex flex-col space-y-3 md:flex-row md:justify-between md:space-y-0 mb-4 md:mb-6">
              <div className="flex items-center space-x-2 md:w-[46%]">
                <Input
                  placeholder="Search templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1"
                />
              </div>
              <div className="md:w-auto">
                <FilterMenu
                  availableTags={availableTags}
                  selectedTags={selectedTags}
                  onTagsChange={setSelectedTags}
                />
              </div>
          </div>
          
          <div className="flex-1 flex py-4 md:py-8 justify-center">
            <div className="space-y-4 w-full">

              {filteredTemplates.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 md:gap-4">
                  {filteredTemplates.map((template) => (
                    <div
                      key={template.id}
                      className={`p-3 md:p-4 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                        selectedTemplate === template.id ? 'ring-1 ring-primary/50 bg-muted/50' : 'hover:bg-muted/20'
                      }`}
                      onClick={() => setSelectedTemplate(template.id)}
                    >
                      <div className="space-y-2">
                        <h3 className="font-semibold text-sm md:text-base">{template.title}</h3>
                        <p className="text-xs md:text-sm text-muted-foreground line-clamp-2">{template.description}</p>
                        <div className="space-y-1">
                          <div className="flex flex-wrap gap-1">
                            {template.tags.map((tag) => (
                              <span
                                key={tag}
                                className="px-1.5 md:px-2 py-0.5 md:py-1 bg-background text-muted-foreground text-xs rounded border capitalize"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground mb-2">No templates found</p>
                  <p className="text-sm text-muted-foreground">
                    Try adjusting your search terms or clearing the filters
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <StepActions
          onBack={onBack}
          onContinue={handleContinue}
          continueLabel="Continue with Selected Template"
          continueDisabled={!selectedTemplate}
        />
        </motion.div>
      </div>
    </div>
  )
}