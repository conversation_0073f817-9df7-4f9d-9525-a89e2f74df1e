import React from 'react'
import { FormBuilder } from '@/lib/components/formbuilder'
import { TEMPLATES } from '@/lib/templates'
import { JsonData } from '@/lib/components/formbuilder/types'

interface FormBuilderRawProps {
  templateId: string | null
  templateName?: string | null
  dataSource?: 'own' | 'template' | null
  onSubmit: (data: any) => void
  onBack: () => void
}

export const FormBuilderRaw: React.FC<FormBuilderRawProps> = ({ 
  templateId, 
  templateName, 
  dataSource, 
  onSubmit, 
  onBack 
}) => {
  const getInitialData = (): JsonData | undefined => {
    if (dataSource === 'template' && templateId && TEMPLATES[templateId as keyof typeof TEMPLATES]) {
      return TEMPLATES[templateId as keyof typeof TEMPLATES].data as JsonData
    }
    return undefined
  }

  return (
    <FormBuilder
      initialData={getInitialData()}
      templateId={templateId}
      templateName={templateName}
      dataSource={dataSource}
      onSubmit={onSubmit}
      onBack={onBack}
    />
  )
}