import { useState, useEffect } from 'react'
import QRCode from 'qrcode'
import { Button } from '@/lib/components/ui/button'
import { 
  QrCode, 
  Share2, 
  Co<PERSON>,
  Loader2,
  Check
} from 'lucide-react'
import { toast } from 'sonner'
import { motion } from 'motion/react'

interface QRCodeShareProps {
  recordId: string
  recordTitle?: string
  isPublic: boolean
  compact?: boolean
}

export const QRCodeShare: React.FC<QRCodeShareProps> = ({ 
  recordId, 
  recordTitle = 'Trace Record',
  isPublic,
  compact = false
}) => {
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [copied, setCopied] = useState(false)
  
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
  const recordUrl = `${baseUrl}/records/${recordId}`
  const qrUrl = `${baseUrl}/records/${recordId}/qr`

  useEffect(() => {
    if (isPublic) {
      generateQRCode()
    }
  }, [recordId, isPublic])

  const generateQRCode = async () => {
    try {
      setLoading(true)
      const qrDataUrl = await QRCode.toDataURL(recordUrl, {
        width: compact ? 128 : 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        errorCorrectionLevel: 'M'
      })
      setQrCodeDataUrl(qrDataUrl)
    } catch (error) {
      toast.error('Failed to generate QR code')
    } finally {
      setLoading(false)
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${recordTitle} - Trace Record`,
          text: `Check out this traceability record on the blockchain`,
          url: recordUrl,
        })
      } catch (error) {
        if (error instanceof Error && error.name !== 'AbortError') {
          fallbackCopyToClipboard()
        }
      }
    } else {
      fallbackCopyToClipboard()
    }
  }

  const fallbackCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(recordUrl)
      setCopied(true)
      toast.success('Link copied to clipboard!')
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      toast.error('Failed to copy link')
    }
  }

  const handleQRClick = () => {
    window.open(qrUrl, '_blank')
  }

  if (!isPublic) {
    return null
  }

  if (compact) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className="flex items-center gap-1"
      >
        <Button
          variant="ghost"
          size="sm"
          onClick={handleShare}
          className="h-8 w-8 p-0"
          title="Share record"
        >
          <Share2 className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost" 
          size="sm"
          onClick={handleQRClick}
          className="h-8 w-8 p-0"
          title="Open QR code"
        >
          <QrCode className="w-4 h-4" />
        </Button>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex flex-col sm:flex-row items-center gap-4 sm:gap-6 p-4 sm:p-6 border rounded-lg bg-muted/20"
    >
      <div className="flex-shrink-0">
        {loading ? (
          <div className="w-20 h-20 sm:w-24 sm:h-24 flex items-center justify-center bg-white rounded-lg border">
            <Loader2 className="w-5 h-5 sm:w-6 sm:h-6 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <motion.button
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            onClick={handleQRClick}
            className="block hover:scale-105 transition-transform cursor-pointer"
            title="Click to open QR page"
          >
            <img
              src={qrCodeDataUrl}
              alt="QR Code for record"
              className="w-20 h-20 sm:w-24 sm:h-24 rounded-lg border bg-white"
            />
          </motion.button>
        )}
      </div>

      <div className="flex-1 min-w-0 text-center sm:text-left">
        <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-3">
          <Button
            onClick={handleShare}
            variant="outline"
            size="sm"
            className="gap-2 w-full sm:w-auto"
          >
            <Share2 className="w-4 h-4" />
            Share
          </Button>
          
          <Button
            onClick={fallbackCopyToClipboard}
            variant="outline"
            size="sm"
            className="gap-2 w-full sm:w-auto"
          >
            {copied ? (
              <Check className="w-4 h-4 text-green-600" />
            ) : (
              <Copy className="w-4 h-4" />
            )}
            {copied ? 'Copied!' : 'Copy Link'}
          </Button>
        </div>
        
        <p className="text-sm text-muted-foreground mt-2">
          Scan QR code or use buttons to share this public record
        </p>
      </div>
    </motion.div>
  )
}

export default QRCodeShare