"use client"

import React, { forwardRef, useRef } from "react"
import { User, Wallet, Box, Globe } from "lucide-react"
import { AnimatedBeam } from "@/lib/components/ui/custom/animated-beams"
import { cn } from "@/lib/utils"

const Circle = forwardRef<
  HTMLDivElement,
  { className?: string; children?: React.ReactNode }
>(({ className, children }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "z-10 flex h-12 w-12 items-center justify-center rounded-full border-2 border-muted-foreground/20 bg-background shadow-[0_0_20px_-12px_rgba(0,0,0,0.8)] dark:bg-muted",
        className
      )}
    >
      {children}
    </div>
  )
})

Circle.displayName = "Circle"

export function AnimatedBeamsDemo({ className }: { className?: string }) {
  const containerRef = useRef<HTMLDivElement>(null)
  const userRef = useRef<HTMLDivElement>(null)
  const block1Ref = useRef<HTMLDivElement>(null)
  const block2Ref = useRef<HTMLDivElement>(null)
  const walletRef = useRef<HTMLDivElement>(null)
  const globeRef = useRef<HTMLDivElement>(null)

  return (
    <div
      className={cn(
        "relative flex h-full w-full items-center justify-end overflow-hidden",
        className
      )}
      ref={containerRef}
    >
      <div className="absolute inset-0 flex items-center justify-end pr-8">
        <div className="relative flex h-full w-full max-w-md items-center justify-between gap-8">
          <Circle ref={userRef}>
            <User className="h-5 w-5" />
          </Circle>

          <div className="flex flex-col items-center justify-center gap-8">
            <Circle ref={block1Ref}>
              <Box className="h-5 w-5 text-blue-500" />
            </Circle>
            <Circle ref={block2Ref}>
              <Box className="h-5 w-5 text-emerald-500" />
            </Circle>
          </div>

          <Circle ref={walletRef}>
            <Wallet className="h-5 w-5 text-purple-500" />
          </Circle>

          <Circle ref={globeRef}>
            <Globe className="h-5 w-5 text-orange-500" />
          </Circle>
        </div>
      </div>
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={userRef}
        toRef={block1Ref}
        curvature={-75}
        pathWidth={2}
        gradientStartColor="#0ea5e9"
        gradientStopColor="#0ea5e9"
        delay={0}
        duration={3}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={userRef}
        toRef={block2Ref}
        curvature={75}
        pathWidth={2}
        gradientStartColor="#10b981"
        gradientStopColor="#10b981"
        delay={0.2}
        duration={3}
      />
      
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={block1Ref}
        toRef={walletRef}
        curvature={-50}
        pathWidth={2}
        gradientStartColor="#a855f7"
        gradientStopColor="#a855f7"
        delay={0.4}
        duration={3}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={block2Ref}
        toRef={walletRef}
        curvature={50}
        pathWidth={2}
        gradientStartColor="#a855f7"
        gradientStopColor="#a855f7"
        delay={0.6}
        duration={3}
      />
      
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={walletRef}
        toRef={globeRef}
        curvature={0}
        pathWidth={2}
        gradientStartColor="#f97316"
        gradientStopColor="#f97316"
        delay={0.8}
        duration={3}
      />
    </div>
  )
}