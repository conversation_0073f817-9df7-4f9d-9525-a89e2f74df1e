"use client";

import { cn } from "@/lib/utils";
import { AnimatedList } from "@/lib/components/ui/custom/animated-list";
import { Badge } from "lucide-react";

interface Item {
  name: string;
  description: Array<string>;
  icon: string;
  color: string;
  time: string;
}

let notifications = [
  {
    name: "Olive Oil",
    description: ["supply chain", "comodity"],
    time: "15m ago",

    icon: "🫒",
    color: "#00C9A7",
  },
  {
    name: "Cocoa",
    description: ["supply chain", "comodity"],
    time: "10m ago",
    icon: "🍫",
    color: "#FFB800",
  },
  {
    name: "Cattle",
    description: ["supply chain", "comodity"],
    time: "5m ago",
    icon: "🐄",
    color: "#FF3D71",
  },
  {
    name: "Honey Harvesting",
    description: ["supply chain", "comodity"],
    time: "2m ago",
    icon: "🐝",
    color: "#1E86FF",
  },
];

notifications = Array.from({ length: 10 }, () => notifications).flat();

const Notification = ({ name, description, icon, color, time }: Item) => {
  return (
    <figure
      className={cn(
        "relative mx-auto min-h-fit w-full max-w-[400px] cursor-pointer overflow-hidden rounded-2xl p-4",
        "transition-all duration-200 ease-in-out hover:scale-[103%]",
        "bg-white [box-shadow:0_0_0_1px_rgba(0,0,0,.03),0_2px_4px_rgba(0,0,0,.05),0_12px_24px_rgba(0,0,0,.05)]",
        "transform-gpu dark:bg-transparent dark:backdrop-blur-md dark:[border:1px_solid_rgba(255,255,255,.1)] dark:[box-shadow:0_-20px_80px_-20px_#ffffff1f_inset]",
      )}
    >
      <div className="flex flex-row items-center gap-3">
        <div
          className="flex size-10 items-center justify-center rounded-2xl"
          style={{
            backgroundColor: color,
          }}
        >
          <span className="text-lg">{icon}</span>
        </div>
        <div className="flex flex-col overflow-hidden">
          <figcaption className="flex space-x-2 flex-row items-center whitespace-pre text-lg font-medium dark:text-white ">
            <span className="text-sm sm:text-lg">{name}</span>
            <span className="text-xs text-primary-foreground px-2 bg-primary rounded-full">TEMPLATE</span>
           
          </figcaption>
          <p className="text-sm font-normal dark:text-white/60 space-x-2">
            {description.map((desc, index) => (
              <span key={index} className="text-xs bg-accent rounded-full px-1 text-muted">
                {desc}
                
              </span>
            ))}
          </p>
        </div>
      </div>
    </figure>
  );
};

export function BentoList({
  className,
}: {
  className?: string;
}) {
  return (
    <div
      className={cn(
        "relative flex h-[500px] w-full flex-col overflow-hidden p-2",
        className,
      )}
    >
      <AnimatedList>
        {notifications.map((item, idx) => (
          <Notification {...item} key={idx} />
        ))}
      </AnimatedList>

      <div className="pointer-events-none absolute inset-x-0 bottom-0 h-1/4 bg-gradient-to-t from-background"></div>
    </div>
  );
}
