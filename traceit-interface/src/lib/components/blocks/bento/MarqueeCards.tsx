import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON> } from "@/lib/components/ui/custom/marquee";

const reviews = [
  {
    name: "<PERSON>",
    username: "@Samb0__",
    body: "Started tracing my weight on chain, first record at traceit.zengate.global/explore/aspdx-39s1.",
    img: "https://pbs.twimg.com/profile_images/1905579339493892096/7fuSWAB8_400x400.jpg",
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    username: "@CardanoChefPool",
    body: "Using Winter Protocol to trace my home grown tomatos, traceit.zengate.global/explore/o19d-lsf3.",
    img: "https://pbs.twimg.com/profile_images/1717399993731198976/3BwS-il8_400x400.jpg",
  },
    {
    name: "<PERSON><PERSON><PERSON>",
    username: "@mgpai22",
    body: "Tracing my intake calories now traceit.zengate.global/explore/z39d-p3kd.",
    img: "https://pbs.twimg.com/profile_images/1940093297222914048/yTgAEGtZ_400x400.jpg",
  },
    {
    name: "Pyro",
    username: "@pyro_ae",
    body: "Just started tracking my fish weights with receipts traceit.zengate.global/explore/x340-9bo3.",
    img: "https://pbs.twimg.com/profile_images/1942697937114652673/k4y1IB9Y_400x400.jpg",
  },
  {
    name: "Daniel",
    username: "@DanielTetsuyama",
    body: "Just started tracing some of my thoughts so I can create a mind map, check it at traceit.zengate.global/explore/93dxf-39fps.",
    img: "https://pbs.twimg.com/profile_images/1467656919553024000/O_liAoWj_400x400.jpg",
  },
];

const firstRow = reviews.slice(0, reviews.length / 2);
const secondRow = reviews.slice(reviews.length / 2);

const ReviewCard = ({
  img,
  name,
  username,
  body,
}: {
  img: string;
  name: string;
  username: string;
  body: string;
}) => {
  return (
    <figure
      className={cn(
        "relative h-full w-fit sm:w-36  cursor-pointer overflow-hidden rounded-xl border p-4",
        "border-gray-950/[.1] bg-gray-950/[.01] hover:bg-gray-950/[.05]",
        "dark:border-gray-50/[.1] dark:bg-gray-50/[.10] dark:hover:bg-gray-50/[.15]",
      )}
    >
      <div className="flex flex-row items-center gap-2">
        <img className="rounded-full" width="32" height="32" alt="" src={img} />
        <div className="flex flex-col">
          <figcaption className="text-sm font-medium dark:text-white">
            {name}
          </figcaption>
          <p className="text-xs font-medium dark:text-white/40">{username}</p>
        </div>
      </div>
      <blockquote className="mt-2 text-sm">{body}</blockquote>
    </figure>
  );
};

export function MarqueeVertical( { className }: { className?: string }) {
  return (
    <div className={`relative flex flex-row items-center justify-center overflow-hidden ${className}`}>
      <Marquee pauseOnHover vertical className="[--duration:20s]">
        {firstRow.map((review) => (
          <ReviewCard key={review.username} {...review} />
        ))}
      </Marquee>
      <Marquee reverse pauseOnHover vertical className="[--duration:20s]">
        {secondRow.map((review) => (
          <ReviewCard key={review.username} {...review} />
        ))}
      </Marquee>
       </div>
  );
}
