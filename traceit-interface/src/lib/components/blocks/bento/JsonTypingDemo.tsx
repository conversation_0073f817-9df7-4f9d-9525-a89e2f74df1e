"use client";

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

const editingSequence = [
  { field: 'id', oldValue: 'trace_001', newValue: 'trace_002', line: 3 },
  { field: 'timestamp', oldValue: '2024-01-15T10:30:00Z', newValue: '2024-01-16T14:22:00Z', line: 4 },
  { field: 'name', oldValue: 'Organic Coffee Beans', newValue: 'Premium Coffee Beans', line: 6 },
  { field: 'batch', oldValue: 'CB-2024-001', newValue: 'CB-2024-002', line: 7 },
  { field: 'origin', oldValue: 'Colombia', newValue: 'Ethiopia', line: 8 },
  { field: 'quantity', oldValue: '500', newValue: '480', line: 9 },
  { field: 'id', oldValue: 'trace_002', newValue: 'trace_003', line: 3 },
  { field: 'name', oldValue: 'Premium Coffee Beans', newValue: 'Specialty Coffee Beans', line: 6 },
];

interface JsonTypingDemoProps {
  className?: string;
}

export const JsonTypingDemo: React.FC<JsonTypingDemoProps> = ({ className }) => {
  const [currentEdit, setCurrentEdit] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [editingText, setEditingText] = useState('');
  const [jsonValues, setJsonValues] = useState({
    id: 'trace_001',
    timestamp: '2024-01-15T10:30:00Z',
    name: 'Organic Coffee Beans',
    batch: 'CB-2024-001',
    origin: 'Colombia',
    quantity: '500'
  });

  const getCurrentJson = () => {
    return `{
  "traceRecord": {
    "id": "${jsonValues.id}",
    "timestamp": "${jsonValues.timestamp}",
    "product": {
      "name": "${jsonValues.name}",
      "batch": "${jsonValues.batch}",
      "origin": "${jsonValues.origin}",
      "quantity": ${jsonValues.quantity}
    },
    "location": {
      "latitude": 4.5709,
      "longitude": -74.2973
    },
    "status": "harvested"
  }
}`;
  };

  useEffect(() => {
    const interval = setInterval(() => {
      const edit = editingSequence[currentEdit];
      setIsEditing(true);
      setEditingText('');
      
      setTimeout(() => {
        let i = 0;
        const typingInterval = setInterval(() => {
          if (i <= edit.newValue.length) {
            setEditingText(edit.newValue.substring(0, i));
            i++;
          } else {
            clearInterval(typingInterval);
            setTimeout(() => {
              setJsonValues(prev => ({
                ...prev,
                [edit.field]: edit.newValue
              }));
              setIsEditing(false);
              setCurrentEdit((prev) => (prev + 1) % editingSequence.length);
            }, 500);
          }
        }, 80);
      }, 300);
    }, 3000);

    return () => clearInterval(interval);
  }, [currentEdit]);

  const renderJsonWithHighlight = () => {
    const currentEditData = editingSequence[currentEdit];
    const currentJsonString = getCurrentJson();
    const lines = currentJsonString.split('\n');
    
    return lines.map((line, index) => {
      const lineNumber = index + 1;
      const isCurrentEditLine = lineNumber === currentEditData.line;
      
      if (isEditing && isCurrentEditLine) {
        const currentValue = jsonValues[currentEditData.field as keyof typeof jsonValues];
        const valueIndex = line.indexOf(currentValue);
        
        if (valueIndex !== -1) {
          const beforeValue = line.substring(0, valueIndex);
          const afterValue = line.substring(valueIndex + currentValue.length);
          
          return (
            <div key={index} className="bg-blue-900/30 rounded px-1">
              <span className="text-gray-300">{beforeValue}</span>
              <span className="bg-yellow-400/20 text-yellow-200 border-l-2 border-yellow-400 pl-1">
                {editingText}
                <span className="animate-pulse">|</span>
              </span>
              <span className="text-gray-300">{afterValue}</span>
            </div>
          );
        }
      }
      
      const colorizedLine = line
        .replace(/"([^"]+)":/g, '<span class="text-blue-400">"$1"</span>:')
        .replace(/: "([^"]+)"/g, ': <span class="text-green-400">"$1"</span>')
        .replace(/: (\d+\.?\d*)/g, ': <span class="text-orange-400">$1</span>')
        .replace(/: (true|false)/g, ': <span class="text-purple-400">$1</span>')
        .replace(/[{}]/g, '<span class="text-gray-300">$&</span>')
        .replace(/,$/g, '<span class="text-gray-400">,</span>');
      
      return (
        <div key={index} dangerouslySetInnerHTML={{ __html: colorizedLine }} />
      );
    });
  };

  return (
    <div className={cn(
      "absolute inset-0 overflow-hidden rounded-lg",
      className
    )}>

      
      <div className="p-8 pt-3 h-full overflow-hidden">

        <pre className="text-xs font-mono leading-relaxed text-gray-100 h-full overflow-hidden whitespace-pre-wrap">
          {renderJsonWithHighlight()}
        </pre>
        
      </div>
    </div>
  );
};