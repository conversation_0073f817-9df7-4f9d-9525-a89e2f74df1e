import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/lib/components/ui/card'
import { BentoCard, BentoGrid } from '@/lib/components/ui/bento-grid'
import { Bell, BookOpenText, Box, Calendar, FileText, Globe, MessageCircleHeart, Wallet } from 'lucide-react';
import { InputIcon } from '@radix-ui/react-icons';
import { BentoList } from './bento/AnimatedList';
import { JsonTypingDemo } from './bento/JsonTypingDemo';
import { MarqueeVertical } from './bento/MarqueeCards';
import { AnimatedBeamsDemo } from './bento/AnimatedBeams';

const features = [
  {
    Icon: Box,
    name: "Publish Records",
    description: "Publish any data format on chain using json schemas.",
    background: <JsonTypingDemo className="absolute right-2 top-4 h-[300px] w-full scale-90 border-none transition-all duration-300 ease-out [mask-image:linear-gradient(to_top,transparent_35%,#000_100%)] group-hover:scale-100" />,
    className: "lg:row-start-1 lg:row-end-3 lg:col-start-1 lg:col-end-2", 
  },
  {
    Icon: BookOpenText,
    name: "Template Library",
    description: "You can use any of our templates to get started quickly.",
    background: <BentoList className="absolute right-2 top-4 h-[300px] w-full scale-75 border-none transition-all duration-300 ease-out [mask-image:linear-gradient(to_top,transparent_35%,#000_100%)] group-hover:scale-90" />,
    className: "lg:col-start-2 lg:col-end-3 lg:row-start-1 lg:row-end-3",
  },
  {
    Icon: Wallet,
    name: "Free Testing",
    description: "You can use the preview version for free without connecting a wallet.",
    background: <AnimatedBeamsDemo className="absolute top-0 right-0 w-full h-full -z-10 scale-95 border-none transition-all duration-300 ease-out [mask-image:linear-gradient(to_top,transparent_3%,#000_100%)]   group-hover:scale-90" />,
    className: "lg:col-start-1 lg:col-end-3 lg:row-start-3 lg:row-end-4",
  },
  {
    Icon: MessageCircleHeart,
    name: "Share your records",
    description: "You can select or not to make your records public when publishing to mainnet.",
    background: <MarqueeVertical className="absolute right-2 top-4 h-[300px] w-full scale-95 border-none transition-all duration-300 ease-out [mask-image:linear-gradient(to_bottom,transparent_0%,#000_35%,#000_75%,transparent_100%)]  group-hover:scale-90 " />,
    className: "lg:col-start-3 lg:col-end-3 lg:row-start-1 lg:row-end-4",
  }
];

export const Guide = () => {
  return (
    <section className="py-12 md:py-16 px-4">
      <div className="max-w-6xl mx-auto">
         <BentoGrid className="lg:grid-rows-4" >
      {features.map((feature) => (
        <BentoCard key={feature.name} {...feature} />
      ))}
    </BentoGrid>
      </div>
    </section>
  )
}