import React from 'react'
import Link from 'next/link'
import { Button } from '@/lib/components/ui/button'

export const Hero = () => {
  return (
    <section className="relative min-h-[50vh] md:min-h-[60vh] flex items-center justify-center">
      <div className="absolute inset-0 bg-transparent" />
      <div className="relative z-10 text-center space-y-4 md:space-y-6 px-4 max-w-4xl mx-auto">
        <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-sans font-medium tracking-tight leading-tight">
          Create your own traceability<br className="hidden sm:block" />
          <span className="sm:hidden"> </span>records on-chain
        </h1>
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-6 md:mt-8">
          <Button size="xxl" className="rounded-full" asChild>
            <Link href="/create">
              Create Records
            </Link>
          </Button>
          <Button size="xxl" variant="outline" className="rounded-full" asChild>
            <Link href="/explorer">
              Explore Records
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}