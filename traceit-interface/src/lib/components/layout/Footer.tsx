import React from 'react'
import Link from 'next/link'

const footerLinks = [
  { name: 'Github', href: 'https://github.com' },
  { name: 'Catalyst', href: 'https://cardano.ideascale.com' },
  { name: 'Documentation', href: '/docs' }
]

export const Footer = () => {
  return (
    <footer className="border-t mt-auto">
      <div className="px-4 py-8 md:py-12">
        <div className="max-w-7xl mx-auto">
          <h3 className="text-xl md:text-2xl font-medium mb-3 md:mb-4 font-sans">Winter Protocol</h3>
          <ul className="space-y-1 md:space-y-2">
            {footerLinks.map((link) => (
              <li key={link.name} className="flex items-center gap-2">
                <span className="w-1 h-1 bg-foreground rounded-full" />
                <Link 
                  href={link.href}
                  className="text-xs md:text-sm font-mono text-muted-foreground hover:text-foreground transition-colors"
                  target={link.href.startsWith('http') ? '_blank' : undefined}
                  rel={link.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                >
                  {link.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </footer>
  )
}