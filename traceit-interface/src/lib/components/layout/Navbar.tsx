"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import { motion, AnimatePresence } from 'motion/react'
import { useNetworkStore } from '@/lib/store/network'
import { Button } from '@/lib/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DrawerTrigger } from '@/lib/components/ui/drawer'
import { Menu, X } from 'lucide-react'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/lib/components/ui/navigation-menu'
import { WalletConnector } from '../blockchain/WalletConnector'

const NetworkSelector = () => {
  const { selectedNetwork, setSelectedNetwork } = useNetworkStore()
  
  return (
    <div className="relative flex items-center bg-black p-1 rounded-full w-fit mx-auto">
      <motion.div
        className="absolute top-1 bottom-1 bg-primary rounded-full shadow-sm"
        initial={false}
        animate={{
          x: selectedNetwork ? 84 : 0,
          width: 80
        }}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      />
      <button
        onClick={() => setSelectedNetwork(false)}
        className={`relative z-10 px-4 py-2 text-sm font-medium transition-colors w-[80px] rounded-full ${
          !selectedNetwork ? 'text-black' : 'text-muted-foreground'
        }`}
      >
        Preview
      </button>
      <button
        onClick={() => setSelectedNetwork(true)}
        className={`relative z-10 px-4 py-2 text-sm font-medium transition-colors w-[80px] rounded-full ${
          selectedNetwork ? 'text-black' : 'text-muted-foreground'
        }`}
      >
        Mainnet
      </button>
    </div>
  )
}

const MobileDrawer = () => {
  const { selectedNetwork } = useNetworkStore()
  const [isOpen, setIsOpen] = useState(false)
  
  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen}>
      <DrawerTrigger asChild>
        <Button variant="ghost" size="sm" className="md:hidden">
          <Menu className="h-5 w-5" />
        </Button>
      </DrawerTrigger>
      <DrawerContent className="min-h-[300px] max-h-[80vh]">
        <div className="flex flex-col gap-8 px-6 py-8">
          <div className="flex justify-center">
            <NetworkSelector />
          </div>
          
          <div className="min-h-[160px] flex flex-col gap-4">
            <Link
              href="/explorer"
              className="block px-4 py-3 text-sm hover:bg-muted rounded-lg transition-colors border border-border/20 text-center"
              onClick={() => setIsOpen(false)}
            >
              Records Explorer
            </Link>
            
            {selectedNetwork ? (
              <>
                <Link
                  href="/records"
                  className="block px-4 py-3 text-sm hover:bg-muted rounded-lg transition-colors border border-border/20 text-center"
                  onClick={() => setIsOpen(false)}
                >
                  Your Records
                </Link>
                
                <WalletConnector/>
              </>
            ) : (
              <>
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-sm text-muted-foreground px-4">
                    <p className="mb-2">💡 Switch to Mainnet</p>
                    <p>to access additional features</p>
                  </div>
                </div>
                <div style={{ display: 'none' }}>
                  <WalletConnector/>
                </div>
              </>
            )}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export const Navbar = () => {
  const { selectedNetwork } = useNetworkStore()
  
  return (
    <nav className="">
      <div className="container flex h-16 items-center md:px-18 px-4 pt-4">
        <div className="flex items-center space-x-4 mr-auto">
          <Link href="/" className="flex items-center space-x-2">
            <span className="text-2xl md:text-4xl font-mono">trace.it</span>
          </Link>
          
          <div className="pl-20 hidden md:block">
          <Link 
            href="/explorer" 
            className="hidden md:block text-sm text-muted-foreground hover:text-foreground transition-colors hover:border-b-1 border-transparent hover:border-primary"
          >
            Explorer
          </Link>
          </div>
        </div>
        
        <div className="hidden md:flex items-center gap-4">
          <AnimatePresence mode="wait">
            {selectedNetwork && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95, x: -10 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0.95, x: -10 }}
                transition={{ 
                  duration: 0.2,
                  ease: [0.23, 1, 0.32, 1]
                }}
              >
                <NavigationMenu>
                  <NavigationMenuList>
                    <NavigationMenuItem>
                      <NavigationMenuLink asChild>
                        <Link href="/records" className={navigationMenuTriggerStyle()}>
                          Your Records
                        </Link>
                      </NavigationMenuLink>
                    </NavigationMenuItem>
                  </NavigationMenuList>
                </NavigationMenu>
              </motion.div>
            )}
          </AnimatePresence>
          
          <div style={{ display: selectedNetwork ? 'block' : 'none' }}>
            <WalletConnector/>
          </div>
          
          <NetworkSelector />
        </div>
        
        <div className="flex md:hidden items-center gap-2">
          <MobileDrawer />
        </div>
      </div>
    </nav>
  )
}