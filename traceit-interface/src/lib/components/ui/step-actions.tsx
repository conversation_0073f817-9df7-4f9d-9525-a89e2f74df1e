import React from 'react'
import { But<PERSON> } from './button'
import { cn } from '@/lib/utils'

interface StepActionsProps {
  onBack?: () => void
  onContinue: () => void
  backLabel?: string
  continueLabel?: string
  continueDisabled?: boolean
  className?: string
  showBack?: boolean
}

export const StepActions: React.FC<StepActionsProps> = ({
  onBack,
  onContinue,
  backLabel = "← Back",
  continueLabel = "Continue",
  continueDisabled = false,
  className,
  showBack = true
}) => {
  return (
    <div className={cn(
      "border-t px-3 md:px-6 py-3 md:py-4 flex flex-col space-y-2 sm:flex-row sm:justify-between sm:space-y-0",
      className
    )}>
      {showBack && onBack ? (
        <Button variant="outline" onClick={onBack} className="w-full sm:w-auto">
          {backLabel}
        </Button>
      ) : (
        <div />
      )}
      <Button 
        onClick={onContinue}
        disabled={continueDisabled}
        className={cn(
          "w-full sm:w-auto",
          continueDisabled && "cursor-not-allowed opacity-50"
        )}
      >
        {continueLabel}
      </Button>
    </div>
  )
}