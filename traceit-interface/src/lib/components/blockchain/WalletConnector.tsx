import React, { useState, useEffect } from 'react';
import { useWallet, useWalletList, useNetwork } from "@meshsdk/react";
import { Button } from '@/lib/components/ui/button';
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogHeader, AlertDialogTitle, AlertDialogCancel } from '@/lib/components/ui/alert-dialog';
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle, DrawerClose } from '@/lib/components/ui/drawer';
import { Alert, AlertDescription } from '@/lib/components/ui/alert';
import { Wallet, ExternalLink, Loader2, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import { useNetworkStore } from '@/lib/store/network';
import { useWalletVerification } from '@/lib/store/wallet-verification';
import { useAutoConnect } from '@/lib/hooks/useAutoConnect';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/lib/components/ui/dropdown-menu"

interface WalletConnectorProps {
  onConnect?: (walletAddress: string) => void;
  onDisconnect?: () => void;
}

export const WalletConnector: React.FC<WalletConnectorProps> = ({ 
  onConnect, 
  onDisconnect 
}) => {
  const { wallet, connected, name, connecting, connect, disconnect, error } = useWallet();
  const  network  = useNetwork();
  const  walletList = useWalletList();
  const { selectedNetwork } = useNetworkStore();
  const { setVerified, clearVerification, isWalletVerified, isVerified: isVerifiedFromStore, walletAddress: verifiedWalletAddress } = useWalletVerification();
  const { autoConnecting, hasValidSession } = useAutoConnect();
  
  const [isOpen, setIsOpen] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string>('');
  const [isMobile, setIsMobile] = useState(false);
  const [networkMismatch, setNetworkMismatch] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [currentNonce, setCurrentNonce] = useState<string>('');
  const [nonceExpiresAt, setNonceExpiresAt] = useState<Date | null>(null);
  const [walletId, setWalletId] = useState<string>('');

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleWalletConnect = async (walletId: string) => {
    try {
      setWalletId(walletId);
      await connect(walletId);
      setIsOpen(false);
    } catch (err: any) {
      if (err?.message?.includes('User canceled') || err?.message?.includes('cancelled')) {
        toast.error('Wallet connection cancelled', {
          duration: 3000
        });
      } else if (err?.message?.includes('already connected')) {
        toast.error('Another wallet is already connected', {
          duration: 4000
        });
      } else {
        toast.error(err?.message || 'Failed to connect wallet', {
          duration: 4000
        });
      }
      
      setIsOpen(false);
    }
  };

  const handleDisconnect = (customMessage?: string) => {
    try {
      disconnect();
    } catch (err: any) {
    }
    setWalletAddress('');
    setNetworkMismatch(false);
    setIsVerified(false);
    setIsVerifying(false);
    setCurrentNonce('');
    setNonceExpiresAt(null);
    setWalletId('');
    clearVerification();
    
    onDisconnect?.();
    if (customMessage) {
      toast.error(customMessage, { duration: 3000 });
    } else {
      toast.success('Wallet disconnected');
    }
  };

  const requestNonce = async (address: string, forceRefresh = false) => {
    try {
      if (!forceRefresh && currentNonce) {
        return currentNonce;
      }

      const response = await fetch('/api/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          walletAddress: address,
          walletName: name || undefined,
        }),
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to get nonce');
      }

      if (data.isVerified) {
        setIsVerified(true);
        setWalletId(data.walletId);
        setCurrentNonce('');
        setNonceExpiresAt(null);
        setVerified(address, walletId);
        
        toast.success('Wallet already verified');
        return null;
      }

      setCurrentNonce(data.nonce);
      setNonceExpiresAt(null);
      return data.nonce;
    } catch (error: any) {
      
      if (error?.message?.includes('Network')) {
        toast.error('Network error. Please check your connection.');
      } else {
        toast.error(error?.message || 'Failed to request verification nonce');
      }
      
      throw error;
    }
  };

  const signAndVerify = async (address: string, nonce: string) => {
    if (!wallet) return;

    try {
      setIsVerifying(true);
      const signatureResult = await wallet.signData(nonce, address);
      const signatureString = JSON.stringify(signatureResult);
      const response = await fetch('/api/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          walletAddress: address,
          signature: signatureString,
          nonce: nonce,
          walletName: name || undefined,
        }),
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Signature verification failed');
      }

      setIsVerified(true);
      setWalletId(data.walletId);
      setCurrentNonce('');
      setNonceExpiresAt(null);
      setVerified(address, walletId);
      
      toast.success('Wallet connected and verified successfully', {
        duration: 3000
      });
      
    } catch (error: any) {
      const errorText = JSON.stringify(error).toLowerCase();
      const isUserDeclined = (error?.name === 'DataSignError' && error?.code === 3) || 
                            errorText.includes('declined') || 
                            errorText.includes('canceled') || 
                            errorText.includes('cancelled') || 
                            errorText.includes('rejected');
      
      if (isUserDeclined) {
        toast.error('User canceled sign up', {
          duration: 3000
        });
      } else if (error?.message?.includes('Invalid signature')) {
        toast.error('Signature verification failed. Please try again.', {
          duration: 4000
        });
      } else if (error?.message?.includes('Network')) {
        toast.error('Network error during verification. Please try again.', {
          duration: 4000
        });
      } else if (error?.message?.includes('expired')) {
        toast.error('Verification expired. Please reconnect your wallet.', {
          duration: 4000
        });
      } else {
        toast.error(error?.message || 'Failed to verify wallet ownership', {
          duration: 4000
        });
      }
      
      throw error;
    } finally {
      setIsVerifying(false);
    }
  };

  const verifyWalletOwnership = async (address: string, forceRefresh = false) => {
    try {
      const nonce = await requestNonce(address, forceRefresh);
      
      if (nonce) {
        await signAndVerify(address, nonce);
      }
    } catch (error: any) {
      if (error?.message?.includes('expired') && !forceRefresh) {
        try {
          return await verifyWalletOwnership(address, true);
        } catch (retryError: any) {
        }
      }
      setIsVerifying(false);
      setCurrentNonce('');
      setNonceExpiresAt(null);
      setIsVerified(false);
      throw error;
    }
  };

  useEffect(() => {
    if (connected && network !== undefined) {
      const isMainnetWallet = network === 1;
      const isMainnetSelected = selectedNetwork;
      if (isMainnetSelected && !isMainnetWallet) {
        setNetworkMismatch(true);
        toast.warning(`Network mismatch! Your wallet is on Preview but you selected Mainnet. Some features may not work correctly.`, {
          duration: 5000
        });
      } else {
        setNetworkMismatch(false);
      }
    }
  }, [connected, network, selectedNetwork]);

  useEffect(() => {
    const getWalletAddressAndCheckVerification = async () => {
      if (connected && wallet && !networkMismatch && !walletAddress) {
        try {
          const changeAddress = await wallet.getChangeAddress();
          setWalletAddress(changeAddress);
          if (isVerifiedFromStore && verifiedWalletAddress === changeAddress) {
            setIsVerified(true);
            const sessionWalletId = useWalletVerification.getState().session?.walletId;
            if (sessionWalletId) {
              setWalletId(sessionWalletId);
            }
            return;
          }
          await verifyWalletOwnership(changeAddress);
        } catch (err: any) {
          const errorText = JSON.stringify(err).toLowerCase();
          const isUserDeclined = (err?.name === 'DataSignError' && err?.code === 3) || 
                                errorText.includes('declined') || 
                                errorText.includes('canceled') || 
                                errorText.includes('cancelled') || 
                                errorText.includes('rejected');
          
          if (isUserDeclined) {
            handleDisconnect();
          } else if (err?.message?.includes('address')) {
            toast.error('Failed to get wallet address. Please try reconnecting.', {
              duration: 4000
            });
            handleDisconnect();
          } else {
            if (!err?.message?.includes('verification') && 
                !err?.message?.includes('signature') && 
                !err?.message?.includes('nonce')) {
              toast.error('Wallet connection failed. Please try again.', {
                duration: 4000
              });
              handleDisconnect();
            }
          }
        }
      }
    };

    getWalletAddressAndCheckVerification();
  }, [connected, wallet, networkMismatch, walletAddress, isVerifiedFromStore, verifiedWalletAddress]);

  useEffect(() => {
    if (isVerified && walletAddress) {
      onConnect?.(walletAddress);
    }
  }, [isVerified, walletAddress, onConnect]);

  const formatAddress = (address: string) => {
    if (address.length <= 20) return address;
    return `${address.slice(0, 5)}..${address.slice(-5)}`;
  };

  const WalletListContent = () => (
    <div className="space-y-4">
      {walletList.length === 0 ? (
        <div className="text-center py-8">
          <Wallet className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground text-sm mb-4">
            No wallets found. We recommend you to try Lace Wallet.
          </p>
          <Button 
            variant="outline" 
            className="w-full"
            onClick={() => window.open('https://www.lace.io/', '_blank')}
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Download Lace Wallet
          </Button>
        </div>
      ) : (
        <div className="space-y-2 ">
          <p className="text-sm text-muted-foreground mb-4">
            Choose a wallet to connect:
          </p>
          {walletList.map((walletItem) => (
            <Button
              key={walletItem.id}
              variant="outline"
              className="flex flex-col w-full h-auto items-center text-center justify-center rounded-full hover:text-white scale-95 hover:scale-100 "
              onClick={() => handleWalletConnect(walletItem.id)}
              disabled={connecting}
            >
              <div className="flex items-center space-x-3">
                <img 
                  src={walletItem.icon} 
                  alt={walletItem.name}
                  className="w-8 h-8 rounded"
                />
                <div className="text-left">
                  <div className="font-medium uppercase font-mono">{walletItem.name}</div>
                </div>
              </div>
            </Button>
          ))}
        </div>
      )}
    </div>
  );

  if (autoConnecting || (connecting || (connected && !walletAddress)) || (connected && walletAddress && !isVerified && !currentNonce)) {
    return (
      <Button disabled className="flex items-center space-x-2 rounded-full">
        <Loader2 className="w-4 h-4 animate-spin" />
        <span>
          {autoConnecting ? 'Reconnecting...' : 
           connecting ? 'Connecting...' : 
           'Initializing...'}
        </span>
      </Button>
    );
  }

  if (connected && walletAddress && (isVerifying || (currentNonce && !isVerified))) {
    return (
      <Button disabled className="flex items-center space-x-2 rounded-full bg-accent text-black">
        <Loader2 className="w-4 h-4 animate-spin" />
        <span>Verifying ownership...</span>
      </Button>
    );
  }

  if (connected && walletAddress && isVerified) {
    return (
      <>
        <DropdownMenu>
          <DropdownMenuTrigger className="px-3 py-2 rounded-full scale-95 hover:scale-100 flex items-center space-x-2 bg-accent text-black">
            <span>{formatAddress(walletAddress)}</span>
            
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              Using {name} (Verified)
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => handleDisconnect()} className="focus:bg-red-700 focus:text-red-200">
              Disconnect
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </>
    );
  }

  return (
    <>
      {networkMismatch && (
        <Alert className="mb-4 border-destructive bg-destructive/10">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Network mismatch detected. Please ensure your wallet is on the same network as selected in the app.
          </AlertDescription>
        </Alert>
      )}

      <Button 
        onClick={() => {
          try {
            setIsOpen(true);
          } catch (err: any) {
            toast.error('Failed to open wallet selection');
          }
        }}
        disabled={connecting || networkMismatch || hasValidSession}
        className="flex items-center space-x-2 rounded-full"
      >
        {connecting ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : hasValidSession ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Wallet className="w-4 h-4" />
        )}
        <span>
          {connecting ? 'Connecting...' : 
           hasValidSession ? 'Restoring session...' : 
           'Connect Wallet'}
        </span>
      </Button>

      {!isMobile && (
        <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
          <AlertDialogContent className="max-w-md">
            <AlertDialogHeader>
              <AlertDialogTitle className="font-sans font-thin text-2xl border-none ring-transparent">Connect Wallet</AlertDialogTitle>
              <AlertDialogDescription>
                Connect your Cardano wallet to continue. Your wallet will be used to sign transactions and prove ownership.
              </AlertDialogDescription>
            </AlertDialogHeader>
            
            <WalletListContent />
            
            <div className="flex justify-end">
              <AlertDialogCancel>Cancel</AlertDialogCancel>
            </div>
          </AlertDialogContent>
        </AlertDialog>
      )}

      {isMobile && (
        <Drawer open={isOpen} onOpenChange={setIsOpen}>
          <DrawerContent>
            <DrawerHeader className="text-left">
              <DrawerTitle>Connect Wallet</DrawerTitle>
              <DrawerDescription>
                Connect your Cardano wallet to continue.
              </DrawerDescription>
            </DrawerHeader>
            
            <div className="px-4 pb-4">
              <WalletListContent />
            </div>
            
            <div className="p-4 border-t">
              <DrawerClose asChild>
                <Button variant="outline" className="w-full">
                  Cancel
                </Button>
              </DrawerClose>
            </div>
          </DrawerContent>
        </Drawer>
      )}

    </>
  );
};
