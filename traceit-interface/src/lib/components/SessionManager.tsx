import { useEffect } from 'react'
import { useWalletVerification } from '@/lib/store/wallet-verification'
import { useAutoConnect } from '@/lib/hooks/useAutoConnect'

export const SessionManager = () => {
  const { clearExpiredSessions, validateSession } = useWalletVerification()
  const { autoConnecting, hasValidSession } = useAutoConnect()

  useEffect(() => {
    const timer = setTimeout(() => {
      clearExpiredSessions()
    }, 500)
    
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      validateSession()
    }, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [])

  return null
}

export default SessionManager