name: Deploy to Cloud Run Staging

on:
  push:
    branches: [ develop ]
  workflow_dispatch:

env:
  PROJECT_ID: zengate-traceit-dev
  REGION: asia-northeast2
  SERVICE_NAME: traceit-staging
  REGISTRY_REGION: asia-southeast2

jobs:
  deploy:
    name: Deploy to Cloud Run
    runs-on: ubuntu-latest
    environment: development
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Configure Docker for Artifact Registry
      run: |
        gcloud auth configure-docker ${{ env.REGISTRY_REGION }}-docker.pkg.dev

    - name: Build Docker image
      run: |
        cd traceit-interface
        docker build -t ${{ env.REGISTRY_REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/traceit/${{ env.SERVICE_NAME }}:${{ github.sha }} .

    - name: Push Docker image to Artifact Registry
      run: |
        docker push ${{ env.REGISTRY_REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/traceit/${{ env.SERVICE_NAME }}:${{ github.sha }}

    - name: Deploy to Cloud Run
      run: |
        gcloud run deploy ${{ env.SERVICE_NAME }} \
          --image ${{ env.REGISTRY_REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/traceit/${{ env.SERVICE_NAME }}:${{ github.sha }} \
          --region ${{ env.REGION }} \
          --platform managed \
          --allow-unauthenticated \
          --port 3000 \
          --memory 1Gi \
          --cpu 1 \
          --max-instances 5 \
          --timeout 300 \
          --set-env-vars NODE_ENV=${{ vars.NODE_ENV }} \
          --set-env-vars PREVIEW_WINTER_URL="${{ vars.PREVIEW_WINTER_URL }}" \
          --set-env-vars MAINNET_WINTER_URL="${{ vars.MAINNET_WINTER_URL }}" \
          --set-env-vars ADA_FEE_AMOUNT="${{ vars.ADA_FEE_AMOUNT }}" \
          --set-env-vars MIN_ADA_FEE_AMOUNT="${{ vars.MIN_ADA_FEE_AMOUNT }}" \
          --set-env-vars PALM_FEE_AMOUNT="${{ vars.PALM_FEE_AMOUNT }}" \
          --set-secrets DATABASE_URL=traceit-staging-db-url:latest \
          --set-secrets BLOCKFROST_PROJECT_ID=traceit-blockfrost-project-id:latest \
          --set-secrets FEE_COLLECTOR_ADDRESS=traceit-fee-collector-address:latest \
          --set-secrets WINTER_ADDRESS=traceit-winter-address:latest \
          --add-cloudsql-instances ${{ vars.CLOUDSQL_INSTANCE }}

    - name: Get Service URL
      id: get-url
      run: |
        SERVICE_URL=$(gcloud run services describe ${{ env.SERVICE_NAME }} \
          --region ${{ env.REGION }} \
          --format="value(status.url)")
        echo "SERVICE_URL=$SERVICE_URL" >> $GITHUB_OUTPUT
        echo "🚀 Service deployed at: $SERVICE_URL"

    - name: Update BASE_URL Secret
      run: |
        # Create or update BASE_URL secret
        if gcloud secrets describe traceit-staging-base-url --project=${{ env.PROJECT_ID }} >/dev/null 2>&1; then
          echo "${{ steps.get-url.outputs.SERVICE_URL }}" | gcloud secrets versions add traceit-staging-base-url --data-file=-
        else
          echo "${{ steps.get-url.outputs.SERVICE_URL }}" | gcloud secrets create traceit-staging-base-url --data-file=-
        fi
        
        # Update Cloud Run service to use BASE_URL secret
        gcloud run services update ${{ env.SERVICE_NAME }} \
          --region ${{ env.REGION }} \
          --set-secrets BASE_URL=traceit-staging-base-url:latest
