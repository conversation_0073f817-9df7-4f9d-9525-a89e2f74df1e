{"name": "trace-mvp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "dev:seed": "tsx scripts/seed.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@meshsdk/core": "^1.9.0-beta.69", "@meshsdk/react": "^1.9.0-beta.69", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "@types/qrcode": "^1.5.5", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.44.4", "lucide-react": "^0.526.0", "motion": "^12.23.9", "next": "15.4.4", "next-themes": "^0.4.6", "pg": "^8.16.3", "qrcode": "^1.5.4", "react": "18.3.1", "react-dom": "18.3.1", "react-use-measure": "^2.1.7", "shiki": "^3.8.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "uuidv7": "^1.0.2", "vaul": "^1.1.2", "zod": "^4.0.10", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.15.5", "@types/react": "18.3.12", "@types/react-dom": "18.3.1", "@types/uuid": "^10.0.0", "dotenv": "^16.4.7", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tsx": "^4.19.2", "tw-animate-css": "^1.3.6", "typescript": "^5"}}