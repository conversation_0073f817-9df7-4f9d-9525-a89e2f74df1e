## General Setup
BASE_URL=http://localhost:3000 ## used to generate qr code links 

## Database Setup 
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/postgres" ## direct connection string

## Winter API setup
PREVIEW_WINTER_URL=https://preview-winter-url.com # winter preview API URL
MAINNET_WINTER_URL=https://mainnet-winter-url.com # winter mainnet API URL

## Blockfrost API
BLOCKFROST_PROJECT_ID=mainnet....abc ## used as a provider to build and submit the fee transaction

## fee collection setup
FEE_COLLECTOR_ADDRESS=addr1qxxut8kl48ttl5z39lk303lylcpv2g5mszgx6e5rxz2kktyae3rzwzy2wg6qz0cg46qqvz5cdgf58crq3fvk6n5gcxeq4anym9 # profit wallet
WINTER_ADDRESS=addr1qxzlltxn2uqym9ukmc6c8ulnrtec4gekdwmfwt6suplwhdne7dp3gs3vp5ayzern2uefxym3wxm849dwep47huyzzqnq93p694 # winter wallet
ADA_FEE_AMOUNT=2    # Amount of ADA to be collected as profit (goes to profit wallet)
MIN_ADA_FEE_AMOUNT=2.7 # Minimum ADA amount that covers mint costs on winter side (goes to winter wallet)
PALM_FEE_AMOUNT=2000 # Amount of PALM tokens to be collected as profit (goes to profit wallet)