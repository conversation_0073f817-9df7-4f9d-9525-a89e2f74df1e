## General Setup
BASE_URL=http://localhost:3000
## Database Setup
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/postgres"
## Winter API setup
PREVIEW_WINTER_URL=https://staging-standalone-winter-cardano-api-829860134030.asia-southeast1.run.app
MAINNET_WINTER_URL=https://production-standalone-winter-cardano-api-56864780523.asia-southeast1.run.app
## Blockfrost API
BLOCKFROST_PROJECT_ID=mainnetjjvjMs3C87YQAYd4M8KkeyIU6EhXnytY
## fee collection setup
FEE_COLLECTOR_ADDRESS= addr1....abcd # profit wallet
WINTER_ADDRESS=addr1qxzlltxn2uqym9ukmc6c8ulnrtec4gekdwmfwt6suplwhdne7dp3gs3vp5ayzern2uefxym3wxm849dwep47huyzzqnq93p694 # winter wallet
ADA_FEE_AMOUNT=2    # Amount of ADA to be collected as profit (goes to profit wallet)
MIN_ADA_FEE_AMOUNT=2.7 # Minimum ADA amount that covers mint costs on winter side (goes to winter wallet)
PALM_FEE_AMOUNT=1000 # Amount of PALM tokens to be collected as profit (goes to profit wallet)